//+------------------------------------------------------------------+
//|                                            PressureOB_Tester.mq5 |
//|                                    Test Script for Pressure OB EA |
//|                                                                    |
//+------------------------------------------------------------------+
#property copyright "Pressure OB Test Script"
#property link      ""
#property version   "1.00"
#property script_show_inputs

//--- Input parameters
input ENUM_TIMEFRAMES TestTimeframe = PERIOD_M15;    // Timeframe to test
input int TestBars = 1000;                           // Number of bars to analyze
input bool ShowResults = true;                       // Show results on chart

//--- Global variables
struct PressureSignal {
    datetime time;
    int type;        // 1 = bullish, -1 = bearish
    double price;
    string description;
};

struct OrderBlockZone {
    datetime time;
    double high;
    double low;
    bool is_bullish;
    string description;
};

PressureSignal pressure_signals[];
OrderBlockZone ob_zones[];

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart() {
    Print("Starting Pressure OB EA Test...");
    
    // Initialize arrays
    ArrayResize(pressure_signals, 0);
    ArrayResize(ob_zones, 0);
    
    // Analyze historical data
    AnalyzePressureSignals();
    AnalyzeOrderBlocks();
    
    // Display results
    if(ShowResults) {
        DisplayResults();
    }
    
    // Generate report
    GenerateReport();
    
    Print("Pressure OB EA Test completed successfully!");
}

//+------------------------------------------------------------------+
//| Analyze pressure signals in historical data                      |
//+------------------------------------------------------------------+
void AnalyzePressureSignals() {
    Print("Analyzing pressure signals...");
    
    int pressure_candles = 2;
    double body_ratio = 0.7;
    double wick_ratio = 0.3;
    double size_min = 20;
    
    for(int i = pressure_candles; i < TestBars; i++) {
        int pressure = DetectPressure(i, pressure_candles, body_ratio, wick_ratio, size_min);
        
        if(pressure != 0) {
            int size = ArraySize(pressure_signals);
            ArrayResize(pressure_signals, size + 1);
            
            pressure_signals[size].time = iTime(_Symbol, TestTimeframe, i);
            pressure_signals[size].type = pressure;
            pressure_signals[size].price = iClose(_Symbol, TestTimeframe, i);
            pressure_signals[size].description = (pressure == 1) ? "Bullish Pressure" : "Bearish Pressure";
        }
    }
    
    Print("Found ", ArraySize(pressure_signals), " pressure signals");
}

//+------------------------------------------------------------------+
//| Detect pressure at specific bar                                  |
//+------------------------------------------------------------------+
int DetectPressure(int start_bar, int candles_needed, double body_ratio, double wick_ratio, double size_min) {
    int bullish_count = 0;
    int bearish_count = 0;
    
    for(int i = start_bar; i < start_bar + candles_needed; i++) {
        double open_price = iOpen(_Symbol, TestTimeframe, i);
        double close_price = iClose(_Symbol, TestTimeframe, i);
        double high_price = iHigh(_Symbol, TestTimeframe, i);
        double low_price = iLow(_Symbol, TestTimeframe, i);
        
        double body_size = MathAbs(close_price - open_price);
        double total_size = high_price - low_price;
        double upper_wick = high_price - MathMax(open_price, close_price);
        double lower_wick = MathMin(open_price, close_price) - low_price;
        
        // Check pressure criteria
        if(total_size < size_min * _Point) continue;
        if(total_size == 0 || body_size / total_size < body_ratio) continue;
        if(body_size == 0 || MathMax(upper_wick, lower_wick) / body_size > wick_ratio) continue;
        
        // Count direction
        if(close_price > open_price) bullish_count++;
        else if(close_price < open_price) bearish_count++;
    }
    
    if(bullish_count >= candles_needed) return 1;   // Bullish pressure
    if(bearish_count >= candles_needed) return -1;  // Bearish pressure
    
    return 0; // No pressure
}

//+------------------------------------------------------------------+
//| Analyze Order Block zones                                        |
//+------------------------------------------------------------------+
void AnalyzeOrderBlocks() {
    Print("Analyzing Order Block zones...");
    
    ENUM_TIMEFRAMES ob_tf = PERIOD_H1;
    int lookback = 50;
    double min_move = 50;
    
    int tf_bars = iBars(_Symbol, ob_tf);
    if(tf_bars < lookback + 10) return;
    
    // Look for bullish OBs
    for(int i = 5; i < lookback; i++) {
        if(IsNewBullishOB(i, ob_tf, min_move)) {
            int size = ArraySize(ob_zones);
            ArrayResize(ob_zones, size + 1);
            
            ob_zones[size].time = iTime(_Symbol, ob_tf, i);
            ob_zones[size].high = iHigh(_Symbol, ob_tf, i);
            ob_zones[size].low = iLow(_Symbol, ob_tf, i);
            ob_zones[size].is_bullish = true;
            ob_zones[size].description = "Bullish OB";
        }
    }
    
    // Look for bearish OBs
    for(int i = 5; i < lookback; i++) {
        if(IsNewBearishOB(i, ob_tf, min_move)) {
            int size = ArraySize(ob_zones);
            ArrayResize(ob_zones, size + 1);
            
            ob_zones[size].time = iTime(_Symbol, ob_tf, i);
            ob_zones[size].high = iHigh(_Symbol, ob_tf, i);
            ob_zones[size].low = iLow(_Symbol, ob_tf, i);
            ob_zones[size].is_bullish = false;
            ob_zones[size].description = "Bearish OB";
        }
    }
    
    Print("Found ", ArraySize(ob_zones), " Order Block zones");
}

//+------------------------------------------------------------------+
//| Check for bullish Order Block                                    |
//+------------------------------------------------------------------+
bool IsNewBullishOB(int bar_index, ENUM_TIMEFRAMES tf, double min_move) {
    double close_i = iClose(_Symbol, tf, bar_index);
    double open_i = iOpen(_Symbol, tf, bar_index);
    double high_i = iHigh(_Symbol, tf, bar_index);
    
    // Must be bearish candle
    if(close_i >= open_i) return false;
    
    // Check for bullish move after
    double highest_after = 0;
    for(int j = bar_index - 1; j >= 1; j--) {
        double high_j = iHigh(_Symbol, tf, j);
        if(high_j > highest_after) highest_after = high_j;
    }
    
    double move_size = highest_after - high_i;
    return (move_size >= min_move * _Point);
}

//+------------------------------------------------------------------+
//| Check for bearish Order Block                                    |
//+------------------------------------------------------------------+
bool IsNewBearishOB(int bar_index, ENUM_TIMEFRAMES tf, double min_move) {
    double close_i = iClose(_Symbol, tf, bar_index);
    double open_i = iOpen(_Symbol, tf, bar_index);
    double low_i = iLow(_Symbol, tf, bar_index);
    
    // Must be bullish candle
    if(close_i <= open_i) return false;
    
    // Check for bearish move after
    double lowest_after = DBL_MAX;
    for(int j = bar_index - 1; j >= 1; j--) {
        double low_j = iLow(_Symbol, tf, j);
        if(low_j < lowest_after) lowest_after = low_j;
    }
    
    double move_size = low_i - lowest_after;
    return (move_size >= min_move * _Point);
}

//+------------------------------------------------------------------+
//| Display results on chart                                         |
//+------------------------------------------------------------------+
void DisplayResults() {
    // Clear existing objects
    ObjectsDeleteAll(0, "TEST_");
    
    // Draw pressure signals
    for(int i = 0; i < ArraySize(pressure_signals); i++) {
        string obj_name = "TEST_PRESSURE_" + IntegerToString(i);
        double price = pressure_signals[i].price;
        
        ObjectCreate(0, obj_name, OBJ_ARROW, 0, pressure_signals[i].time, price);
        ObjectSetInteger(0, obj_name, OBJPROP_ARROWCODE, 
                        (pressure_signals[i].type == 1) ? 233 : 234);
        ObjectSetInteger(0, obj_name, OBJPROP_COLOR, 
                        (pressure_signals[i].type == 1) ? clrLime : clrRed);
        ObjectSetInteger(0, obj_name, OBJPROP_WIDTH, 3);
    }
    
    // Draw OB zones
    for(int i = 0; i < ArraySize(ob_zones); i++) {
        string obj_name = "TEST_OB_" + IntegerToString(i);
        
        ObjectCreate(0, obj_name, OBJ_RECTANGLE, 0, 
                    ob_zones[i].time, ob_zones[i].high,
                    TimeCurrent(), ob_zones[i].low);
        ObjectSetInteger(0, obj_name, OBJPROP_COLOR, 
                        ob_zones[i].is_bullish ? clrBlue : clrRed);
        ObjectSetInteger(0, obj_name, OBJPROP_STYLE, STYLE_DOT);
        ObjectSetInteger(0, obj_name, OBJPROP_WIDTH, 1);
        ObjectSetInteger(0, obj_name, OBJPROP_FILL, false);
        ObjectSetInteger(0, obj_name, OBJPROP_BACK, true);
    }
}

//+------------------------------------------------------------------+
//| Generate test report                                             |
//+------------------------------------------------------------------+
void GenerateReport() {
    Print("=== PRESSURE OB EA TEST REPORT ===");
    Print("Symbol: ", _Symbol);
    Print("Timeframe: ", EnumToString(TestTimeframe));
    Print("Bars analyzed: ", TestBars);
    Print("");
    Print("PRESSURE SIGNALS:");
    Print("Total signals: ", ArraySize(pressure_signals));
    
    int bullish_pressure = 0, bearish_pressure = 0;
    for(int i = 0; i < ArraySize(pressure_signals); i++) {
        if(pressure_signals[i].type == 1) bullish_pressure++;
        else bearish_pressure++;
    }
    
    Print("Bullish pressure: ", bullish_pressure);
    Print("Bearish pressure: ", bearish_pressure);
    Print("");
    Print("ORDER BLOCKS:");
    Print("Total OB zones: ", ArraySize(ob_zones));
    
    int bullish_obs = 0, bearish_obs = 0;
    for(int i = 0; i < ArraySize(ob_zones); i++) {
        if(ob_zones[i].is_bullish) bullish_obs++;
        else bearish_obs++;
    }
    
    Print("Bullish OBs: ", bullish_obs);
    Print("Bearish OBs: ", bearish_obs);
    Print("");
    Print("RECOMMENDATIONS:");
    
    if(ArraySize(pressure_signals) < 10) {
        Print("- Consider reducing pressure requirements");
    }
    if(ArraySize(ob_zones) < 5) {
        Print("- Consider reducing OB minimum move requirement");
    }
    if(bullish_pressure > bearish_pressure * 2) {
        Print("- Market shows bullish bias in test period");
    } else if(bearish_pressure > bullish_pressure * 2) {
        Print("- Market shows bearish bias in test period");
    }
    
    Print("=== END OF REPORT ===");
}
