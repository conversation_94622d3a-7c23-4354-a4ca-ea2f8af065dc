# Pressure + Order Block Expert Advisor (MQ5)

## Overview
The Pressure OB EA is a sophisticated trading system that combines **Pressure Analysis** with **Order Block (OB) zones** to identify high-probability trading opportunities in the Forex market.

## Strategy Logic

### 1. Pressure Detection
**Bullish Pressure**: 2 or more consecutive large bullish candles with:
- Body ratio ≥ 70% of total candle size
- Small wicks (≤ 30% of body size)
- Minimum candle size in points

**Bearish Pressure**: 2 or more consecutive large bearish candles with:
- Body ratio ≥ 70% of total candle size  
- Small wicks (≤ 30% of body size)
- Minimum candle size in points

### 2. Order Block Identification
**Bullish OB**: Last bearish candle before a strong bullish move
**Bearish OB**: Last bullish candle before a strong bearish move

Order Blocks are identified from higher timeframes (H1/H4) and used as key support/resistance zones.

### 3. Trade Entry Rules
**BUY Signal**:
- Bullish pressure detected
- Price pulls back to a valid Bullish OB zone
- Optional: Rejection candle confirmation at OB

**SELL Signal**:
- Bearish pressure detected  
- Price pulls back to a valid Bearish OB zone
- Optional: Rejection candle confirmation at OB

## Input Parameters

### Pressure Settings
- **PressureCandles**: Number of candles required for pressure confirmation (default: 2)
- **PressureBodyRatio**: Minimum body to total candle ratio (default: 0.7 = 70%)
- **PressureWickRatio**: Maximum wick to body ratio (default: 0.3 = 30%)
- **PressureSizeMin**: Minimum candle size in points (default: 20)

### Order Block Settings
- **OB_Timeframe**: Timeframe for OB identification (default: H1)
- **OB_LookbackBars**: Bars to look back for OB detection (default: 50)
- **OB_MinMove**: Minimum move after OB in points (default: 50)
- **OB_ValidityBars**: OB validity period in bars (default: 100)
- **ShowOBZones**: Display OB zones on chart (default: true)

### Entry Settings
- **RequireRejection**: Require rejection candle at OB (default: true)
- **RejectionWickRatio**: Rejection wick to body ratio (default: 0.5)
- **ConfirmationBars**: Bars to wait for confirmation (default: 3)

### Risk Management
- **RiskPercent**: Risk per trade as % of balance (default: 2.0%)
- **FixedLotSize**: Fixed lot size if RiskPercent = 0 (default: 0.1)
- **SL_Type**: Stop Loss type (FIXED/ATR/OB_SIZE)
- **SL_Fixed**: Fixed SL in points (default: 50)
- **ATR_Period**: ATR period for dynamic SL (default: 14)
- **ATR_Multiplier**: ATR multiplier for SL (default: 2.0)
- **TP_Type**: Take Profit type (FIXED/RR/ATR)
- **TP_Fixed**: Fixed TP in points (default: 100)
- **RiskReward**: Risk:Reward ratio (default: 2.0)
- **UseBreakeven**: Enable breakeven (default: true)
- **BreakevenPoints**: Breakeven trigger in points (default: 20)
- **UseTrailing**: Enable trailing stop (default: true)
- **TrailingStart**: Trailing start in points (default: 30)
- **TrailingStep**: Trailing step in points (default: 10)

### Trade Filters
- **MaxTradesPerDay**: Maximum trades per day (default: 3)
- **StartTime**: Trading start time (default: "08:00")
- **EndTime**: Trading end time (default: "18:00")
- **UseNewsFilter**: Enable news filter (default: false)
- **NewsMinutesBefore**: Minutes before news (default: 30)
- **NewsMinutesAfter**: Minutes after news (default: 30)

### Display Settings
- **ShowDashboard**: Show information dashboard (default: true)
- **ShowAlerts**: Show trade alerts (default: true)
- **BullishOBColor**: Bullish OB zone color (default: Blue)
- **BearishOBColor**: Bearish OB zone color (default: Red)

## Installation Instructions

1. **Copy the EA file** to your MetaTrader 5 `MQL5/Experts/` folder
2. **Restart MetaTrader 5** or refresh the Navigator
3. **Drag the EA** onto your chart
4. **Configure the parameters** according to your preferences
5. **Enable AutoTrading** in MetaTrader 5

## Recommended Settings

### For Major Forex Pairs (EUR/USD, GBP/USD, USD/JPY)
- **Timeframe**: M15 or M30 for entries
- **OB_Timeframe**: H1 or H4
- **RiskPercent**: 1-2%
- **SL_Type**: ATR
- **TP_Type**: RR with 2:1 ratio

### For Volatile Pairs (GBP/JPY, EUR/JPY)
- **PressureSizeMin**: 30-40 points
- **ATR_Multiplier**: 2.5-3.0
- **RiskPercent**: 1-1.5%

### For Less Volatile Pairs (EUR/CHF, USD/CHF)
- **PressureSizeMin**: 15-20 points
- **ATR_Multiplier**: 1.5-2.0
- **RiskPercent**: 2-2.5%

## Visual Features

### Dashboard Information
- Current trades count
- Pressure status (Bullish/Bearish/None)
- Order Blocks count
- Current time
- Trading status (Active/Inactive)

### Chart Objects
- **Blue rectangles**: Bullish Order Block zones
- **Red rectangles**: Bearish Order Block zones
- **Arrows**: Pressure direction indicators (optional)

## Risk Warnings

1. **Backtesting**: Always backtest the EA on historical data before live trading
2. **Demo Trading**: Test on demo account first with small lot sizes
3. **Market Conditions**: The EA works best in trending markets
4. **News Events**: Consider using news filter during high-impact events
5. **Broker Compatibility**: Ensure your broker supports automated trading

## Optimization Tips

1. **Timeframe Combination**: Use M15/M30 for entries with H1/H4 OBs
2. **Currency Pairs**: Focus on major pairs with good liquidity
3. **Trading Hours**: Optimize for your broker's active trading hours
4. **Risk Management**: Never risk more than 2-3% per trade
5. **Regular Monitoring**: Check EA performance regularly

## Troubleshooting

### Common Issues
- **No trades**: Check if within trading hours and pressure criteria are met
- **High drawdown**: Reduce risk percentage or adjust SL settings
- **Too many trades**: Increase pressure requirements or add filters
- **OB zones not showing**: Ensure ShowOBZones is enabled

### Performance Optimization
- **Reduce lookback periods** for faster processing
- **Limit OB validity** to prevent old zones from cluttering
- **Use appropriate timeframes** for your trading style

## Support and Updates

For questions, suggestions, or bug reports, please refer to the EA comments section or contact the developer.

**Version**: 1.0
**Last Updated**: 2024
**Compatibility**: MetaTrader 5 Build 3000+
