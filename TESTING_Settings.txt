🧪 PRESSURE OB EA - TESTING & DEBUGGING SETTINGS 🧪
====================================================

🔧 SETTINGS TO GET THE EA TRADING AGAIN 🔧

=== MAIN MODE SETTINGS ===
TestMode = true                    // Enable test mode with minimal filters
UseHighWinRateMode = false         // Disable high win rate mode for testing
SimpleMode = true                  // Keep simple mode enabled

=== RELAXED PRESSURE SETTINGS ===
PressureCandles = 1                // Only need 1 candle (easier to detect)
PressureBodyRatio = 0.5            // 50% body ratio (relaxed)
PressureWickRatio = 0.5            // 50% wick ratio (relaxed)
PressureSizeMin = 5                // Small minimum size (more signals)
RequireConsecutive = false         // Don't require consecutive candles
RequireTrendAlignment = false      // Don't require trend alignment

=== DISABLED FILTERS (FOR TESTING) ===
UseMarketSessionFilter = false     // Trade 24/7
UseSpreadFilter = false            // Ignore spread
UseVolumeFilter = false            // Ignore volume (optional)
UseMomentumFilter = false          // Ignore momentum (optional)
UseConfluenceFilter = false        // Ignore confluence
MinConfluenceScore = 1             // Minimum score (very low)

=== BASIC RISK SETTINGS ===
RiskPercent = 1.0                  // 1% risk
FixedLotSize = 0.01               // Small lot for testing
SL_Type = SL_FIXED                // Use fixed SL
SL_Fixed = 50                     // 50 points SL
TP_Type = TP_FIXED                // Use fixed TP
TP_Fixed = 100                    // 100 points TP

=== TIME SETTINGS ===
MaxTradesPerDay = 10              // Allow more trades for testing
StartTime = "00:00"               // Trade 24/7
EndTime = "23:59"                 // Trade 24/7

=== DEBUGGING SETTINGS ===
ShowDashboard = true              // Show dashboard
ShowAlerts = true                 // Show alerts
ShowOBZones = true                // Show OB zones

=== STEP-BY-STEP TESTING GUIDE ===

🔧 STEP 1: BASIC FUNCTIONALITY TEST
1. Load EA with TestMode = true
2. Watch Expert tab for "🧪 TEST MODE" messages
3. Should see pressure detection messages
4. Should execute test trades with minimal filters

🔧 STEP 2: IF NO TRADES IN TEST MODE
- Check if AutoTrading is enabled
- Check if account allows trading
- Run QuickTradeTest.mq5 script first
- Check Expert tab for error messages

🔧 STEP 3: GRADUALLY ENABLE FEATURES
Once test mode works:
1. Set TestMode = false
2. Set SimpleMode = true
3. Keep all filters disabled initially
4. Gradually enable filters one by one

🔧 STEP 4: ENABLE ADVANCED FEATURES
After basic trading works:
1. Enable UseVolumeFilter = true
2. Enable UseMomentumFilter = true
3. Enable UseMarketSessionFilter = true
4. Increase MinConfluenceScore gradually

🔧 STEP 5: HIGH WIN RATE MODE
Finally, when everything works:
1. Set UseHighWinRateMode = true
2. Increase MinConfluenceScore = 3-4
3. Enable all quality filters
4. Monitor win rate performance

=== DEBUGGING CHECKLIST ===

✅ Check Expert tab for messages:
- "🧪 TEST MODE: Checking signals..."
- "🧪 TEST MODE PRESSURE: BULLISH/BEARISH"
- "🧪 TEST BUY/SELL EXECUTION!"

✅ If no pressure detected:
- Reduce PressureSizeMin to 1
- Set PressureBodyRatio to 0.3
- Set PressureWickRatio to 0.7

✅ If trades fail:
- Check trade error codes in Expert tab
- Verify account balance and margin
- Check symbol trading permissions
- Verify lot size is valid for broker

✅ Common issues:
- "Trade context busy" - Wait and retry
- "Invalid stops" - Check SL/TP levels
- "Not enough money" - Reduce lot size
- "Market closed" - Check trading hours

=== EXPECTED BEHAVIOR IN TEST MODE ===

🧪 TEST MODE ACTIVE:
- Prints pressure analysis every bar
- Executes trades with 50 point SL, 100 point TP
- Uses 0.01 lot size
- 5-minute minimum between trades
- Minimal filters applied

📊 MESSAGES YOU SHOULD SEE:
- "🧪 TEST MODE: Checking signals with minimal filters..."
- "🧪 TEST MODE PRESSURE: BULLISH PRESSURE" (when detected)
- "🧪 TEST MODE: EXECUTING BUY TRADE!" (when trading)
- "🧪 TEST BUY EXECUTED SUCCESSFULLY!" (when successful)

🎯 GOAL: Get the EA trading in test mode first, then gradually add complexity!

Remember: Start simple, then add complexity step by step! 🚀
