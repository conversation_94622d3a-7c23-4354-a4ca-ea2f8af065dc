//+------------------------------------------------------------------+
//|                                              QuickTradeTest.mq5 |
//|                                    Quick test to verify trading  |
//|                                                                    |
//+------------------------------------------------------------------+
#property copyright "Quick Trade Test"
#property link      ""
#property version   "1.00"
#property script_show_inputs

#include <Trade\Trade.mqh>

//--- Input parameters
input double TestLotSize = 0.01;                  // Test lot size
input int TestSLPoints = 50;                      // Test Stop Loss points
input int TestTPPoints = 100;                     // Test Take Profit points

//--- Global variables
CTrade trade;

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart() {
    Print("=== QUICK TRADE TEST STARTED ===");
    
    // Set up trade object
    trade.SetExpertMagicNumber(999999);
    trade.SetDeviationInPoints(10);
    trade.SetTypeFilling(ORDER_FILLING_FOK);
    
    // Check if we can trade
    if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED)) {
        Print("ERROR: Trading is not allowed in terminal!");
        return;
    }
    
    if(!MQLInfoInteger(MQL_TRADE_ALLOWED)) {
        Print("ERROR: Trading is not allowed for this EA!");
        return;
    }
    
    if(!AccountInfoInteger(ACCOUNT_TRADE_ALLOWED)) {
        Print("ERROR: Trading is not allowed for this account!");
        return;
    }
    
    if(!SymbolInfoInteger(_Symbol, SYMBOL_TRADE_MODE)) {
        Print("ERROR: Trading is not allowed for symbol ", _Symbol);
        return;
    }
    
    Print("All trading permissions OK");
    
    // Check current positions
    int positions = PositionsTotal();
    Print("Current positions: ", positions);
    
    if(positions > 0) {
        Print("Closing existing positions first...");
        CloseAllPositions();
        Sleep(1000); // Wait a bit
    }
    
    // Test buy trade
    Print("Testing BUY trade...");
    TestBuyTrade();
    
    Sleep(2000); // Wait 2 seconds
    
    // Close the test trade
    Print("Closing test trade...");
    CloseAllPositions();
    
    Sleep(2000); // Wait 2 seconds
    
    // Test sell trade
    Print("Testing SELL trade...");
    TestSellTrade();
    
    Sleep(2000); // Wait 2 seconds
    
    // Close the test trade
    Print("Closing test trade...");
    CloseAllPositions();
    
    Print("=== QUICK TRADE TEST COMPLETED ===");
}

//+------------------------------------------------------------------+
//| Test buy trade                                                   |
//+------------------------------------------------------------------+
void TestBuyTrade() {
    double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl_price = entry_price - TestSLPoints * _Point;
    double tp_price = entry_price + TestTPPoints * _Point;
    
    Print("BUY Parameters:");
    Print("  Entry: ", entry_price);
    Print("  SL: ", sl_price);
    Print("  TP: ", tp_price);
    Print("  Lot: ", TestLotSize);
    
    if(trade.Buy(TestLotSize, _Symbol, entry_price, sl_price, tp_price, "Test Buy")) {
        Print("✓ BUY trade executed successfully!");
        Print("  Ticket: ", trade.ResultOrder());
    } else {
        Print("✗ BUY trade failed!");
        Print("  Error: ", trade.ResultRetcode());
        Print("  Description: ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| Test sell trade                                                  |
//+------------------------------------------------------------------+
void TestSellTrade() {
    double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl_price = entry_price + TestSLPoints * _Point;
    double tp_price = entry_price - TestTPPoints * _Point;
    
    Print("SELL Parameters:");
    Print("  Entry: ", entry_price);
    Print("  SL: ", sl_price);
    Print("  TP: ", tp_price);
    Print("  Lot: ", TestLotSize);
    
    if(trade.Sell(TestLotSize, _Symbol, entry_price, sl_price, tp_price, "Test Sell")) {
        Print("✓ SELL trade executed successfully!");
        Print("  Ticket: ", trade.ResultOrder());
    } else {
        Print("✗ SELL trade failed!");
        Print("  Error: ", trade.ResultRetcode());
        Print("  Description: ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| Close all positions                                              |
//+------------------------------------------------------------------+
void CloseAllPositions() {
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0) {
            if(trade.PositionClose(ticket)) {
                Print("Position ", ticket, " closed successfully");
            } else {
                Print("Failed to close position ", ticket, ". Error: ", trade.ResultRetcode());
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check symbol trading conditions                                  |
//+------------------------------------------------------------------+
void CheckSymbolInfo() {
    Print("=== SYMBOL INFORMATION ===");
    Print("Symbol: ", _Symbol);
    Print("Digits: ", SymbolInfoInteger(_Symbol, SYMBOL_DIGITS));
    Print("Point: ", _Point);
    Print("Min lot: ", SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN));
    Print("Max lot: ", SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX));
    Print("Lot step: ", SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP));
    Print("Spread: ", SymbolInfoInteger(_Symbol, SYMBOL_SPREAD));
    Print("Trade mode: ", SymbolInfoInteger(_Symbol, SYMBOL_TRADE_MODE));
    Print("Bid: ", SymbolInfoDouble(_Symbol, SYMBOL_BID));
    Print("Ask: ", SymbolInfoDouble(_Symbol, SYMBOL_ASK));
    
    Print("=== ACCOUNT INFORMATION ===");
    Print("Balance: ", AccountInfoDouble(ACCOUNT_BALANCE));
    Print("Equity: ", AccountInfoDouble(ACCOUNT_EQUITY));
    Print("Free margin: ", AccountInfoDouble(ACCOUNT_MARGIN_FREE));
    Print("Leverage: ", AccountInfoInteger(ACCOUNT_LEVERAGE));
    Print("Trade allowed: ", AccountInfoInteger(ACCOUNT_TRADE_ALLOWED));
    
    Print("=== TERMINAL INFORMATION ===");
    Print("Trade allowed: ", TerminalInfoInteger(TERMINAL_TRADE_ALLOWED));
    Print("MQL trade allowed: ", MQLInfoInteger(MQL_TRADE_ALLOWED));
}
