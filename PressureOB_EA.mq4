//+------------------------------------------------------------------+
//|                                                  PressureOB_EA.mq4 |
//|                                    Expert Advisor for Pressure + OB |
//|                                                                      |
//+------------------------------------------------------------------+
#property copyright "Pressure OB Trading System"
#property link      ""
#property version   "1.00"
#property strict

//--- Input Parameters
input group "=== PRESSURE SETTINGS ==="
input int PressureCandles = 2;                    // Number of candles for pressure confirmation
input double PressureBodyRatio = 0.7;             // Body to total candle ratio (0.7 = 70%)
input double PressureWickRatio = 0.3;             // Max wick to body ratio (0.3 = 30%)
input double PressureSizeMin = 20;                 // Minimum candle size in points

input group "=== ORDER BLOCK SETTINGS ==="
input ENUM_TIMEFRAMES OB_Timeframe = PERIOD_H1;   // Order Block timeframe
input int OB_LookbackBars = 50;                   // Bars to look back for OB
input double OB_MinMove = 50;                     // Minimum move after OB (points)
input int OB_ValidityBars = 100;                  // OB validity period (bars)
input bool ShowOBZones = true;                    // Show OB zones on chart

input group "=== ENTRY SETTINGS ==="
input bool RequireRejection = true;               // Require rejection candle at OB
input double RejectionWickRatio = 0.5;            // Rejection wick to body ratio
input int ConfirmationBars = 3;                   // Bars to wait for confirmation

input group "=== RISK MANAGEMENT ==="
input double RiskPercent = 2.0;                   // Risk per trade (% of balance)
input double FixedLotSize = 0.1;                  // Fixed lot size (if RiskPercent = 0)
input ENUM_SL_TYPE { SL_FIXED, SL_ATR, SL_OB_SIZE } SL_Type = SL_ATR;
input double SL_Fixed = 50;                       // Fixed SL in points
input int ATR_Period = 14;                        // ATR period for dynamic SL
input double ATR_Multiplier = 2.0;                // ATR multiplier for SL
input ENUM_TP_TYPE { TP_FIXED, TP_RR, TP_ATR } TP_Type = TP_RR;
input double TP_Fixed = 100;                      // Fixed TP in points
input double RiskReward = 2.0;                    // Risk:Reward ratio
input bool UseBreakeven = true;                   // Use breakeven
input double BreakevenPoints = 20;                // Breakeven trigger (points)
input bool UseTrailing = true;                    // Use trailing stop
input double TrailingStart = 30;                  // Trailing start (points)
input double TrailingStep = 10;                   // Trailing step (points)

input group "=== TRADE FILTERS ==="
input int MaxTradesPerDay = 3;                    // Maximum trades per day
input string StartTime = "08:00";                 // Trading start time
input string EndTime = "18:00";                   // Trading end time
input bool UseNewsFilter = false;                 // Use news filter
input int NewsMinutesBefore = 30;                 // Minutes before news
input int NewsMinutesAfter = 30;                  // Minutes after news

input group "=== DISPLAY SETTINGS ==="
input bool ShowDashboard = true;                  // Show dashboard
input bool ShowAlerts = true;                     // Show alerts
input color BullishOBColor = clrBlue;             // Bullish OB color
input color BearishOBColor = clrRed;              // Bearish OB color

//--- Global Variables
struct OrderBlock {
    datetime time;
    double high;
    double low;
    bool is_bullish;
    bool is_valid;
    int creation_bar;
};

OrderBlock bullish_obs[];
OrderBlock bearish_obs[];
int trades_today = 0;
datetime last_trade_date = 0;
string ea_name = "PressureOB_EA";

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    Print("Pressure OB EA initialized successfully");
    
    // Initialize arrays
    ArrayResize(bullish_obs, 0);
    ArrayResize(bearish_obs, 0);
    
    // Create dashboard if enabled
    if(ShowDashboard) {
        CreateDashboard();
    }
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    // Clean up objects
    ObjectsDeleteAll(0, ea_name);
    Print("Pressure OB EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    // Check if new bar
    static datetime last_bar_time = 0;
    if(Time[0] == last_bar_time) return;
    last_bar_time = Time[0];
    
    // Reset daily trade counter
    if(TimeToStr(TimeCurrent(), TIME_DATE) != TimeToStr(last_trade_date, TIME_DATE)) {
        trades_today = 0;
        last_trade_date = TimeCurrent();
    }
    
    // Check trading time
    if(!IsTradeTime()) return;
    
    // Check maximum trades per day
    if(trades_today >= MaxTradesPerDay) return;
    
    // Update Order Blocks
    UpdateOrderBlocks();
    
    // Check for trade signals
    CheckTradeSignals();
    
    // Manage existing trades
    ManageTrades();
    
    // Update dashboard
    if(ShowDashboard) {
        UpdateDashboard();
    }
}

//+------------------------------------------------------------------+
//| Check if current time is within trading hours                    |
//+------------------------------------------------------------------+
bool IsTradeTime() {
    datetime current_time = TimeCurrent();
    string current_time_str = TimeToStr(current_time, TIME_MINUTES);
    
    return (current_time_str >= StartTime && current_time_str <= EndTime);
}

//+------------------------------------------------------------------+
//| Detect pressure (bullish or bearish)                            |
//+------------------------------------------------------------------+
int DetectPressure(int start_bar = 1) {
    int bullish_count = 0;
    int bearish_count = 0;
    
    for(int i = start_bar; i < start_bar + PressureCandles; i++) {
        if(i >= Bars) break;
        
        double body_size = MathAbs(Close[i] - Open[i]);
        double total_size = High[i] - Low[i];
        double upper_wick = High[i] - MathMax(Open[i], Close[i]);
        double lower_wick = MathMin(Open[i], Close[i]) - Low[i];
        
        // Check if candle meets pressure criteria
        if(total_size < PressureSizeMin * Point) continue;
        if(body_size / total_size < PressureBodyRatio) continue;
        if(MathMax(upper_wick, lower_wick) / body_size > PressureWickRatio) continue;
        
        // Count bullish/bearish candles
        if(Close[i] > Open[i]) bullish_count++;
        else if(Close[i] < Open[i]) bearish_count++;
    }
    
    if(bullish_count >= PressureCandles) return 1;  // Bullish pressure
    if(bearish_count >= PressureCandles) return -1; // Bearish pressure
    
    return 0; // No pressure
}

//+------------------------------------------------------------------+
//| Update Order Blocks from higher timeframe                        |
//+------------------------------------------------------------------+
void UpdateOrderBlocks() {
    int tf_bars = iBars(Symbol(), OB_Timeframe);
    if(tf_bars < OB_LookbackBars + 10) return;

    // Clear old invalid OBs
    CleanupOldOrderBlocks();

    // Look for new bullish Order Blocks
    for(int i = 5; i < OB_LookbackBars; i++) {
        if(IsNewBullishOB(i)) {
            AddBullishOB(i);
        }
    }

    // Look for new bearish Order Blocks
    for(int i = 5; i < OB_LookbackBars; i++) {
        if(IsNewBearishOB(i)) {
            AddBearishOB(i);
        }
    }

    // Draw OB zones if enabled
    if(ShowOBZones) {
        DrawOrderBlocks();
    }
}

//+------------------------------------------------------------------+
//| Check if bar forms a new bullish Order Block                    |
//+------------------------------------------------------------------+
bool IsNewBullishOB(int bar_index) {
    double high_i = iHigh(Symbol(), OB_Timeframe, bar_index);
    double low_i = iLow(Symbol(), OB_Timeframe, bar_index);
    double close_i = iClose(Symbol(), OB_Timeframe, bar_index);
    double open_i = iOpen(Symbol(), OB_Timeframe, bar_index);

    // Must be a bearish candle
    if(close_i >= open_i) return false;

    // Check for strong bullish move after this candle
    double highest_after = 0;
    for(int j = bar_index - 1; j >= 1; j--) {
        double high_j = iHigh(Symbol(), OB_Timeframe, j);
        if(high_j > highest_after) highest_after = high_j;
    }

    double move_size = highest_after - high_i;
    return (move_size >= OB_MinMove * Point);
}

//+------------------------------------------------------------------+
//| Check if bar forms a new bearish Order Block                    |
//+------------------------------------------------------------------+
bool IsNewBearishOB(int bar_index) {
    double high_i = iHigh(Symbol(), OB_Timeframe, bar_index);
    double low_i = iLow(Symbol(), OB_Timeframe, bar_index);
    double close_i = iClose(Symbol(), OB_Timeframe, bar_index);
    double open_i = iOpen(Symbol(), OB_Timeframe, bar_index);

    // Must be a bullish candle
    if(close_i <= open_i) return false;

    // Check for strong bearish move after this candle
    double lowest_after = 999999;
    for(int j = bar_index - 1; j >= 1; j--) {
        double low_j = iLow(Symbol(), OB_Timeframe, j);
        if(low_j < lowest_after) lowest_after = low_j;
    }

    double move_size = low_i - lowest_after;
    return (move_size >= OB_MinMove * Point);
}

//+------------------------------------------------------------------+
//| Add new bullish Order Block                                      |
//+------------------------------------------------------------------+
void AddBullishOB(int bar_index) {
    int size = ArraySize(bullish_obs);
    ArrayResize(bullish_obs, size + 1);

    bullish_obs[size].time = iTime(Symbol(), OB_Timeframe, bar_index);
    bullish_obs[size].high = iHigh(Symbol(), OB_Timeframe, bar_index);
    bullish_obs[size].low = iLow(Symbol(), OB_Timeframe, bar_index);
    bullish_obs[size].is_bullish = true;
    bullish_obs[size].is_valid = true;
    bullish_obs[size].creation_bar = Bars;
}

//+------------------------------------------------------------------+
//| Add new bearish Order Block                                      |
//+------------------------------------------------------------------+
void AddBearishOB(int bar_index) {
    int size = ArraySize(bearish_obs);
    ArrayResize(bearish_obs, size + 1);

    bearish_obs[size].time = iTime(Symbol(), OB_Timeframe, bar_index);
    bearish_obs[size].high = iHigh(Symbol(), OB_Timeframe, bar_index);
    bearish_obs[size].low = iLow(Symbol(), OB_Timeframe, bar_index);
    bearish_obs[size].is_bullish = false;
    bearish_obs[size].is_valid = true;
    bearish_obs[size].creation_bar = Bars;
}

//+------------------------------------------------------------------+
//| Clean up old invalid Order Blocks                               |
//+------------------------------------------------------------------+
void CleanupOldOrderBlocks() {
    // Remove old bullish OBs
    for(int i = ArraySize(bullish_obs) - 1; i >= 0; i--) {
        if(Bars - bullish_obs[i].creation_bar > OB_ValidityBars) {
            ArrayRemove(bullish_obs, i, 1);
        }
    }

    // Remove old bearish OBs
    for(int i = ArraySize(bearish_obs) - 1; i >= 0; i--) {
        if(Bars - bearish_obs[i].creation_bar > OB_ValidityBars) {
            ArrayRemove(bearish_obs, i, 1);
        }
    }
}
