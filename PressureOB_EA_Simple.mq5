//+------------------------------------------------------------------+
//|                                        PressureOB_EA_Simple.mq5 |
//|                                    Simplified version for testing |
//|                                                                    |
//+------------------------------------------------------------------+
#property copyright "Pressure OB Simple Test"
#property link      ""
#property version   "1.00"

#include <Trade\Trade.mqh>

//--- Input Parameters
input double RiskPercent = 1.0;                   // Risk per trade (% of balance)
input double FixedLotSize = 0.01;                 // Fixed lot size
input int StopLossPoints = 50;                    // Stop Loss in points
input int TakeProfitPoints = 100;                 // Take Profit in points
input bool EnableDebug = true;                    // Enable debug prints

//--- Global Variables
CTrade trade;
int magic_number = 123456;
datetime last_trade_time = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    Print("Simple Pressure OB EA initialized");
    
    trade.SetExpertMagicNumber(magic_number);
    trade.SetDeviationInPoints(10);
    trade.SetTypeFilling(ORDER_FILLING_FOK);
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    Print("Simple Pressure OB EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    // Only trade once per bar
    static datetime last_bar_time = 0;
    datetime current_bar_time = iTime(_Symbol, PERIOD_CURRENT, 0);
    if(current_bar_time == last_bar_time) return;
    last_bar_time = current_bar_time;
    
    if(EnableDebug) Print("DEBUG: New bar at ", TimeToString(current_bar_time));
    
    // Don't trade if we already have positions
    if(PositionsTotal() > 0) {
        if(EnableDebug) Print("DEBUG: Already have ", PositionsTotal(), " positions");
        return;
    }
    
    // Simple pressure detection
    int pressure = SimpleDetectPressure();
    
    if(EnableDebug) {
        string pressure_text = "None";
        if(pressure == 1) pressure_text = "Bullish";
        else if(pressure == -1) pressure_text = "Bearish";
        Print("DEBUG: Pressure: ", pressure_text);
    }
    
    // Execute trades based on pressure
    if(pressure == 1) {
        ExecuteSimpleBuy();
    } else if(pressure == -1) {
        ExecuteSimpleSell();
    }
}

//+------------------------------------------------------------------+
//| Simple pressure detection                                        |
//+------------------------------------------------------------------+
int SimpleDetectPressure() {
    // Check last 2 candles for simple pressure
    int bullish_count = 0;
    int bearish_count = 0;
    
    for(int i = 1; i <= 2; i++) {
        double open_price = iOpen(_Symbol, PERIOD_CURRENT, i);
        double close_price = iClose(_Symbol, PERIOD_CURRENT, i);
        double high_price = iHigh(_Symbol, PERIOD_CURRENT, i);
        double low_price = iLow(_Symbol, PERIOD_CURRENT, i);
        
        double body_size = MathAbs(close_price - open_price);
        double total_size = high_price - low_price;
        
        // Simple criteria: body must be at least 50% of total candle
        if(total_size > 0 && body_size / total_size >= 0.5) {
            if(close_price > open_price) bullish_count++;
            else if(close_price < open_price) bearish_count++;
        }
    }
    
    if(bullish_count >= 2) return 1;   // Bullish pressure
    if(bearish_count >= 2) return -1;  // Bearish pressure
    
    return 0; // No pressure
}

//+------------------------------------------------------------------+
//| Execute simple buy trade                                         |
//+------------------------------------------------------------------+
void ExecuteSimpleBuy() {
    if(EnableDebug) Print("DEBUG: Attempting to execute BUY trade");
    
    // Prevent multiple trades too close together
    if(TimeCurrent() - last_trade_time < 300) { // 5 minutes
        if(EnableDebug) Print("DEBUG: Too soon since last trade");
        return;
    }
    
    double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl_price = entry_price - StopLossPoints * _Point;
    double tp_price = entry_price + TakeProfitPoints * _Point;
    double lot_size = CalculateSimpleLotSize();
    
    if(EnableDebug) {
        Print("DEBUG: BUY Parameters - Entry: ", entry_price, 
              ", SL: ", sl_price, ", TP: ", tp_price, ", Lot: ", lot_size);
    }
    
    if(trade.Buy(lot_size, _Symbol, entry_price, sl_price, tp_price, "Simple PressureOB Buy")) {
        last_trade_time = TimeCurrent();
        Print("BUY trade executed successfully at ", entry_price);
        Alert("Simple PressureOB: BUY executed at ", entry_price);
    } else {
        Print("BUY trade failed! Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| Execute simple sell trade                                        |
//+------------------------------------------------------------------+
void ExecuteSimpleSell() {
    if(EnableDebug) Print("DEBUG: Attempting to execute SELL trade");
    
    // Prevent multiple trades too close together
    if(TimeCurrent() - last_trade_time < 300) { // 5 minutes
        if(EnableDebug) Print("DEBUG: Too soon since last trade");
        return;
    }
    
    double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl_price = entry_price + StopLossPoints * _Point;
    double tp_price = entry_price - TakeProfitPoints * _Point;
    double lot_size = CalculateSimpleLotSize();
    
    if(EnableDebug) {
        Print("DEBUG: SELL Parameters - Entry: ", entry_price, 
              ", SL: ", sl_price, ", TP: ", tp_price, ", Lot: ", lot_size);
    }
    
    if(trade.Sell(lot_size, _Symbol, entry_price, sl_price, tp_price, "Simple PressureOB Sell")) {
        last_trade_time = TimeCurrent();
        Print("SELL trade executed successfully at ", entry_price);
        Alert("Simple PressureOB: SELL executed at ", entry_price);
    } else {
        Print("SELL trade failed! Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| Calculate simple lot size                                        |
//+------------------------------------------------------------------+
double CalculateSimpleLotSize() {
    if(RiskPercent <= 0) return FixedLotSize;
    
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = balance * RiskPercent / 100.0;
    double sl_distance = StopLossPoints * _Point;
    
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    
    if(tick_size > 0 && tick_value > 0) {
        double lot_size = risk_amount / (sl_distance / tick_size * tick_value);
        
        // Normalize lot size
        double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
        double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
        double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
        
        lot_size = MathMax(min_lot, MathMin(max_lot, lot_size));
        if(lot_step > 0) {
            lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;
        }
        
        return lot_size;
    }
    
    return FixedLotSize;
}

//+------------------------------------------------------------------+
//| Test function to manually trigger trades                         |
//+------------------------------------------------------------------+
void TestTrade() {
    Print("=== MANUAL TEST TRADE ===");
    
    if(PositionsTotal() > 0) {
        Print("Cannot test - already have positions");
        return;
    }
    
    // Force a buy trade for testing
    double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl_price = entry_price - 50 * _Point;
    double tp_price = entry_price + 100 * _Point;
    double lot_size = 0.01;
    
    Print("Test trade parameters - Entry: ", entry_price, ", SL: ", sl_price, ", TP: ", tp_price);
    
    if(trade.Buy(lot_size, _Symbol, entry_price, sl_price, tp_price, "Test Trade")) {
        Print("Test BUY trade executed successfully!");
    } else {
        Print("Test trade failed! Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
    }
}
