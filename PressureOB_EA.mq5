//+------------------------------------------------------------------+
//|                                                  PressureOB_EA.mq5 |
//|                        Pressure + Order Block Trading System     |
//|                                             Expert Advisor v1.0   |
//+------------------------------------------------------------------+
#property copyright "Pressure OB Trading System"
#property link      ""
#property version   "1.00"

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>

//--- Input Parameters
input group "=== PRESSURE SETTINGS ==="
input int PressureCandles = 2;                    // Number of candles for pressure confirmation
input double PressureBodyRatio = 0.7;             // Body to total candle ratio (0.7 = 70%)
input double PressureWickRatio = 0.3;             // Max wick to body ratio (0.3 = 30%)
input double PressureSizeMin = 20;                 // Minimum candle size in points

input group "=== ORDER BLOCK SETTINGS ==="
input ENUM_TIMEFRAMES OB_Timeframe = PERIOD_H1;   // Order Block timeframe
input int OB_LookbackBars = 50;                   // Bars to look back for OB
input double OB_MinMove = 50;                     // Minimum move after OB (points)
input int OB_ValidityBars = 100;                  // OB validity period (bars)
input bool ShowOBZones = true;                    // Show OB zones on chart

input group "=== ENTRY SETTINGS ==="
input bool RequireRejection = true;               // Require rejection candle at OB
input double RejectionWickRatio = 0.5;            // Rejection wick to body ratio
input int ConfirmationBars = 3;                   // Bars to wait for confirmation

input group "=== RISK MANAGEMENT ==="
input double RiskPercent = 2.0;                   // Risk per trade (% of balance)
input double FixedLotSize = 0.1;                  // Fixed lot size (if RiskPercent = 0)
enum ENUM_SL_TYPE { SL_FIXED, SL_ATR, SL_OB_SIZE };
input ENUM_SL_TYPE SL_Type = SL_ATR;              // Stop Loss type
input double SL_Fixed = 50;                       // Fixed SL in points
input int ATR_Period = 14;                        // ATR period for dynamic SL
input double ATR_Multiplier = 2.0;                // ATR multiplier for SL
enum ENUM_TP_TYPE { TP_FIXED, TP_RR, TP_ATR };
input ENUM_TP_TYPE TP_Type = TP_RR;               // Take Profit type
input double TP_Fixed = 100;                      // Fixed TP in points
input double RiskReward = 2.0;                    // Risk:Reward ratio
input bool UseBreakeven = true;                   // Use breakeven
input double BreakevenPoints = 20;                // Breakeven trigger (points)
input bool UseTrailing = true;                    // Use trailing stop
input double TrailingStart = 30;                  // Trailing start (points)
input double TrailingStep = 10;                   // Trailing step (points)

input group "=== TRADE FILTERS ==="
input int MaxTradesPerDay = 3;                    // Maximum trades per day
input string StartTime = "08:00";                 // Trading start time
input string EndTime = "18:00";                   // Trading end time
input bool UseNewsFilter = false;                 // Use news filter
input int NewsMinutesBefore = 30;                 // Minutes before news
input int NewsMinutesAfter = 30;                  // Minutes after news

input group "=== DISPLAY SETTINGS ==="
input bool ShowDashboard = true;                  // Show dashboard
input bool ShowAlerts = true;                     // Show alerts
input color BullishOBColor = clrBlue;             // Bullish OB color
input color BearishOBColor = clrRed;              // Bearish OB color

//--- Global Variables
struct OrderBlock {
    datetime time;
    double high;
    double low;
    bool is_bullish;
    bool is_valid;
    int creation_bar;
};

OrderBlock bullish_obs[];
OrderBlock bearish_obs[];
int trades_today = 0;
datetime last_trade_date = 0;
string ea_name = "PressureOB_EA";
int magic_number = 123456;

CTrade trade;
CPositionInfo position;
CAccountInfo account;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    Print("Pressure OB EA (MQ5) initialized successfully");
    
    // Initialize trade object
    trade.SetExpertMagicNumber(magic_number);
    trade.SetDeviationInPoints(10);
    trade.SetTypeFilling(ORDER_FILLING_FOK);
    
    // Initialize arrays
    ArrayResize(bullish_obs, 0);
    ArrayResize(bearish_obs, 0);
    
    // Create dashboard if enabled
    if(ShowDashboard) {
        CreateDashboard();
    }
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    // Clean up objects
    ObjectsDeleteAll(0, ea_name);
    Print("Pressure OB EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    // Check if new bar
    static datetime last_bar_time = 0;
    datetime current_bar_time = iTime(_Symbol, PERIOD_CURRENT, 0);
    if(current_bar_time == last_bar_time) return;
    last_bar_time = current_bar_time;

    // DEBUG: Print new bar info
    static int debug_counter = 0;
    debug_counter++;
    if(debug_counter % 10 == 0) { // Print every 10th bar to avoid spam
        Print("DEBUG: New bar at ", TimeToString(current_bar_time));
    }

    // Reset daily trade counter
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    string current_date = StringFormat("%04d.%02d.%02d", dt.year, dt.mon, dt.day);

    MqlDateTime last_dt;
    TimeToStruct(last_trade_date, last_dt);
    string last_date = StringFormat("%04d.%02d.%02d", last_dt.year, last_dt.mon, last_dt.day);

    if(current_date != last_date) {
        trades_today = 0;
        last_trade_date = TimeCurrent();
        Print("DEBUG: New day, reset trades_today to 0");
    }

    // Check trading time
    if(!IsTradeTime()) {
        if(debug_counter % 50 == 0) Print("DEBUG: Outside trading hours");
        return;
    }

    // Check maximum trades per day
    if(trades_today >= MaxTradesPerDay) {
        if(debug_counter % 50 == 0) Print("DEBUG: Max trades per day reached: ", trades_today);
        return;
    }

    // Update Order Blocks
    UpdateOrderBlocks();

    // DEBUG: Print OB status
    if(debug_counter % 20 == 0) {
        Print("DEBUG: Bullish OBs: ", ArraySize(bullish_obs), ", Bearish OBs: ", ArraySize(bearish_obs));
    }

    // Check for trade signals
    CheckTradeSignals();

    // Manage existing trades
    ManageTrades();

    // Update dashboard
    if(ShowDashboard) {
        UpdateDashboard();
    }
}

//+------------------------------------------------------------------+
//| Check if current time is within trading hours                    |
//+------------------------------------------------------------------+
bool IsTradeTime() {
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    string current_time = StringFormat("%02d:%02d", dt.hour, dt.min);
    
    return (current_time >= StartTime && current_time <= EndTime);
}

//+------------------------------------------------------------------+
//| Detect pressure (bullish or bearish)                            |
//+------------------------------------------------------------------+
int DetectPressure(int start_bar = 1) {
    int bullish_count = 0;
    int bearish_count = 0;
    
    for(int i = start_bar; i < start_bar + PressureCandles; i++) {
        double open_price = iOpen(_Symbol, PERIOD_CURRENT, i);
        double close_price = iClose(_Symbol, PERIOD_CURRENT, i);
        double high_price = iHigh(_Symbol, PERIOD_CURRENT, i);
        double low_price = iLow(_Symbol, PERIOD_CURRENT, i);
        
        double body_size = MathAbs(close_price - open_price);
        double total_size = high_price - low_price;
        double upper_wick = high_price - MathMax(open_price, close_price);
        double lower_wick = MathMin(open_price, close_price) - low_price;
        
        // Check if candle meets pressure criteria
        if(total_size < PressureSizeMin * _Point) continue;
        if(total_size == 0 || body_size / total_size < PressureBodyRatio) continue;
        if(body_size == 0 || MathMax(upper_wick, lower_wick) / body_size > PressureWickRatio) continue;
        
        // Count bullish/bearish candles
        if(close_price > open_price) bullish_count++;
        else if(close_price < open_price) bearish_count++;
    }
    
    if(bullish_count >= PressureCandles) return 1;  // Bullish pressure
    if(bearish_count >= PressureCandles) return -1; // Bearish pressure
    
    return 0; // No pressure
}

//+------------------------------------------------------------------+
//| Update Order Blocks from higher timeframe                        |
//+------------------------------------------------------------------+
void UpdateOrderBlocks() {
    int tf_bars = iBars(_Symbol, OB_Timeframe);
    if(tf_bars < OB_LookbackBars + 10) return;
    
    // Clear old invalid OBs
    CleanupOldOrderBlocks();
    
    // Look for new bullish Order Blocks
    for(int i = 5; i < OB_LookbackBars; i++) {
        if(IsNewBullishOB(i)) {
            AddBullishOB(i);
        }
    }
    
    // Look for new bearish Order Blocks  
    for(int i = 5; i < OB_LookbackBars; i++) {
        if(IsNewBearishOB(i)) {
            AddBearishOB(i);
        }
    }
    
    // Draw OB zones if enabled
    if(ShowOBZones) {
        DrawOrderBlocks();
    }
}

//+------------------------------------------------------------------+
//| Check if bar forms a new bullish Order Block                    |
//+------------------------------------------------------------------+
bool IsNewBullishOB(int bar_index) {
    double high_i = iHigh(_Symbol, OB_Timeframe, bar_index);
    double low_i = iLow(_Symbol, OB_Timeframe, bar_index);
    double close_i = iClose(_Symbol, OB_Timeframe, bar_index);
    double open_i = iOpen(_Symbol, OB_Timeframe, bar_index);
    
    // Must be a bearish candle
    if(close_i >= open_i) return false;
    
    // Check for strong bullish move after this candle
    double highest_after = 0;
    for(int j = bar_index - 1; j >= 1; j--) {
        double high_j = iHigh(_Symbol, OB_Timeframe, j);
        if(high_j > highest_after) highest_after = high_j;
    }
    
    double move_size = highest_after - high_i;
    return (move_size >= OB_MinMove * _Point);
}

//+------------------------------------------------------------------+
//| Check if bar forms a new bearish Order Block                    |
//+------------------------------------------------------------------+
bool IsNewBearishOB(int bar_index) {
    double high_i = iHigh(_Symbol, OB_Timeframe, bar_index);
    double low_i = iLow(_Symbol, OB_Timeframe, bar_index);
    double close_i = iClose(_Symbol, OB_Timeframe, bar_index);
    double open_i = iOpen(_Symbol, OB_Timeframe, bar_index);
    
    // Must be a bullish candle
    if(close_i <= open_i) return false;
    
    // Check for strong bearish move after this candle
    double lowest_after = DBL_MAX;
    for(int j = bar_index - 1; j >= 1; j--) {
        double low_j = iLow(_Symbol, OB_Timeframe, j);
        if(low_j < lowest_after) lowest_after = low_j;
    }
    
    double move_size = low_i - lowest_after;
    return (move_size >= OB_MinMove * _Point);
}

//+------------------------------------------------------------------+
//| Add new bullish Order Block                                      |
//+------------------------------------------------------------------+
void AddBullishOB(int bar_index) {
    int size = ArraySize(bullish_obs);
    ArrayResize(bullish_obs, size + 1);

    bullish_obs[size].time = iTime(_Symbol, OB_Timeframe, bar_index);
    bullish_obs[size].high = iHigh(_Symbol, OB_Timeframe, bar_index);
    bullish_obs[size].low = iLow(_Symbol, OB_Timeframe, bar_index);
    bullish_obs[size].is_bullish = true;
    bullish_obs[size].is_valid = true;
    bullish_obs[size].creation_bar = iBars(_Symbol, PERIOD_CURRENT);
}

//+------------------------------------------------------------------+
//| Add new bearish Order Block                                      |
//+------------------------------------------------------------------+
void AddBearishOB(int bar_index) {
    int size = ArraySize(bearish_obs);
    ArrayResize(bearish_obs, size + 1);

    bearish_obs[size].time = iTime(_Symbol, OB_Timeframe, bar_index);
    bearish_obs[size].high = iHigh(_Symbol, OB_Timeframe, bar_index);
    bearish_obs[size].low = iLow(_Symbol, OB_Timeframe, bar_index);
    bearish_obs[size].is_bullish = false;
    bearish_obs[size].is_valid = true;
    bearish_obs[size].creation_bar = iBars(_Symbol, PERIOD_CURRENT);
}

//+------------------------------------------------------------------+
//| Clean up old invalid Order Blocks                               |
//+------------------------------------------------------------------+
void CleanupOldOrderBlocks() {
    int current_bars = iBars(_Symbol, PERIOD_CURRENT);

    // Remove old bullish OBs
    for(int i = ArraySize(bullish_obs) - 1; i >= 0; i--) {
        if(current_bars - bullish_obs[i].creation_bar > OB_ValidityBars) {
            ArrayRemove(bullish_obs, i, 1);
        }
    }

    // Remove old bearish OBs
    for(int i = ArraySize(bearish_obs) - 1; i >= 0; i--) {
        if(current_bars - bearish_obs[i].creation_bar > OB_ValidityBars) {
            ArrayRemove(bearish_obs, i, 1);
        }
    }
}

//+------------------------------------------------------------------+
//| Check for trade signals                                          |
//+------------------------------------------------------------------+
void CheckTradeSignals() {
    int pressure = DetectPressure();

    // DEBUG: Print pressure status
    static int last_pressure = 999;
    if(pressure != last_pressure) {
        string pressure_text = "No Pressure";
        if(pressure == 1) pressure_text = "Bullish Pressure";
        else if(pressure == -1) pressure_text = "Bearish Pressure";
        Print("DEBUG: Pressure changed to: ", pressure_text);
        last_pressure = pressure;
    }

    if(pressure == 1) { // Bullish pressure
        Print("DEBUG: Checking buy signal...");
        CheckBuySignal();
    } else if(pressure == -1) { // Bearish pressure
        Print("DEBUG: Checking sell signal...");
        CheckSellSignal();
    }
}

//+------------------------------------------------------------------+
//| Check for buy signal                                             |
//+------------------------------------------------------------------+
void CheckBuySignal() {
    double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    Print("DEBUG: CheckBuySignal - Current price: ", current_price, ", Bullish OBs: ", ArraySize(bullish_obs));

    // Check if price is near a bullish OB
    for(int i = 0; i < ArraySize(bullish_obs); i++) {
        if(!bullish_obs[i].is_valid) {
            Print("DEBUG: Bullish OB ", i, " is invalid");
            continue;
        }

        Print("DEBUG: Bullish OB ", i, " - High: ", bullish_obs[i].high, ", Low: ", bullish_obs[i].low);

        // Check if price is within OB zone
        if(current_price >= bullish_obs[i].low && current_price <= bullish_obs[i].high) {
            Print("DEBUG: Price is within Bullish OB zone!");

            // Check for rejection if required
            if(RequireRejection && !CheckRejectionCandle(true)) {
                Print("DEBUG: Rejection candle required but not found");
                continue;
            }

            Print("DEBUG: All conditions met, executing buy trade!");
            // Execute buy trade
            ExecuteBuyTrade(bullish_obs[i]);
            break;
        }
    }
}

//+------------------------------------------------------------------+
//| Check for sell signal                                            |
//+------------------------------------------------------------------+
void CheckSellSignal() {
    double current_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    Print("DEBUG: CheckSellSignal - Current price: ", current_price, ", Bearish OBs: ", ArraySize(bearish_obs));

    // Check if price is near a bearish OB
    for(int i = 0; i < ArraySize(bearish_obs); i++) {
        if(!bearish_obs[i].is_valid) {
            Print("DEBUG: Bearish OB ", i, " is invalid");
            continue;
        }

        Print("DEBUG: Bearish OB ", i, " - High: ", bearish_obs[i].high, ", Low: ", bearish_obs[i].low);

        // Check if price is within OB zone
        if(current_price >= bearish_obs[i].low && current_price <= bearish_obs[i].high) {
            Print("DEBUG: Price is within Bearish OB zone!");

            // Check for rejection if required
            if(RequireRejection && !CheckRejectionCandle(false)) {
                Print("DEBUG: Rejection candle required but not found");
                continue;
            }

            Print("DEBUG: All conditions met, executing sell trade!");
            // Execute sell trade
            ExecuteSellTrade(bearish_obs[i]);
            break;
        }
    }
}

//+------------------------------------------------------------------+
//| Check for rejection candle                                       |
//+------------------------------------------------------------------+
bool CheckRejectionCandle(bool is_bullish_setup) {
    double open_price = iOpen(_Symbol, PERIOD_CURRENT, 1);
    double close_price = iClose(_Symbol, PERIOD_CURRENT, 1);
    double high_price = iHigh(_Symbol, PERIOD_CURRENT, 1);
    double low_price = iLow(_Symbol, PERIOD_CURRENT, 1);

    double body_size = MathAbs(close_price - open_price);
    double upper_wick = high_price - MathMax(open_price, close_price);
    double lower_wick = MathMin(open_price, close_price) - low_price;

    if(body_size == 0) return false;

    if(is_bullish_setup) {
        // Look for bullish rejection (long lower wick)
        return (lower_wick / body_size >= RejectionWickRatio && close_price > open_price);
    } else {
        // Look for bearish rejection (long upper wick)
        return (upper_wick / body_size >= RejectionWickRatio && close_price < open_price);
    }
}

//+------------------------------------------------------------------+
//| Execute buy trade                                                |
//+------------------------------------------------------------------+
void ExecuteBuyTrade(OrderBlock &ob) {
    Print("DEBUG: ExecuteBuyTrade called!");

    if(PositionsTotal() > 0) {
        Print("DEBUG: Cannot execute buy trade - already have ", PositionsTotal(), " open positions");
        return; // Only one trade at a time
    }

    double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl_price = CalculateStopLoss(true, ob);
    double tp_price = CalculateTakeProfit(true, entry_price, sl_price);
    double lot_size = CalculateLotSize(entry_price, sl_price);

    Print("DEBUG: Buy trade parameters - Entry: ", entry_price, ", SL: ", sl_price, ", TP: ", tp_price, ", Lot: ", lot_size);

    if(trade.Buy(lot_size, _Symbol, entry_price, sl_price, tp_price, "PressureOB Buy")) {
        trades_today++;
        if(ShowAlerts) Alert("PressureOB: BUY signal executed at ", entry_price);
        Print("Buy trade executed: Entry=", entry_price, " SL=", sl_price, " TP=", tp_price);
    } else {
        Print("DEBUG: Buy trade failed! Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| Execute sell trade                                               |
//+------------------------------------------------------------------+
void ExecuteSellTrade(OrderBlock &ob) {
    Print("DEBUG: ExecuteSellTrade called!");

    if(PositionsTotal() > 0) {
        Print("DEBUG: Cannot execute sell trade - already have ", PositionsTotal(), " open positions");
        return; // Only one trade at a time
    }

    double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl_price = CalculateStopLoss(false, ob);
    double tp_price = CalculateTakeProfit(false, entry_price, sl_price);
    double lot_size = CalculateLotSize(entry_price, sl_price);

    Print("DEBUG: Sell trade parameters - Entry: ", entry_price, ", SL: ", sl_price, ", TP: ", tp_price, ", Lot: ", lot_size);

    if(trade.Sell(lot_size, _Symbol, entry_price, sl_price, tp_price, "PressureOB Sell")) {
        trades_today++;
        if(ShowAlerts) Alert("PressureOB: SELL signal executed at ", entry_price);
        Print("Sell trade executed: Entry=", entry_price, " SL=", sl_price, " TP=", tp_price);
    } else {
        Print("DEBUG: Sell trade failed! Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| Calculate Stop Loss                                              |
//+------------------------------------------------------------------+
double CalculateStopLoss(bool is_buy, OrderBlock &ob) {
    double sl_price = 0;

    switch(SL_Type) {
        case SL_FIXED:
            if(is_buy) {
                sl_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SL_Fixed * _Point;
            } else {
                sl_price = SymbolInfoDouble(_Symbol, SYMBOL_BID) + SL_Fixed * _Point;
            }
            break;

        case SL_ATR: {
            int atr_handle = iATR(_Symbol, PERIOD_CURRENT, ATR_Period);
            double atr_buffer[];
            ArraySetAsSeries(atr_buffer, true);
            CopyBuffer(atr_handle, 0, 1, 1, atr_buffer);
            double atr_value = atr_buffer[0];

            if(is_buy) {
                sl_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK) - atr_value * ATR_Multiplier;
            } else {
                sl_price = SymbolInfoDouble(_Symbol, SYMBOL_BID) + atr_value * ATR_Multiplier;
            }
            break;
        }

        case SL_OB_SIZE:
            if(is_buy) {
                sl_price = ob.low - 5 * _Point; // Below OB zone
            } else {
                sl_price = ob.high + 5 * _Point; // Above OB zone
            }
            break;
    }

    return sl_price;
}

//+------------------------------------------------------------------+
//| Calculate Take Profit                                            |
//+------------------------------------------------------------------+
double CalculateTakeProfit(bool is_buy, double entry_price, double sl_price) {
    double tp_price = 0;
    double sl_distance = MathAbs(entry_price - sl_price);

    switch(TP_Type) {
        case TP_FIXED:
            if(is_buy) {
                tp_price = entry_price + TP_Fixed * _Point;
            } else {
                tp_price = entry_price - TP_Fixed * _Point;
            }
            break;

        case TP_RR:
            if(is_buy) {
                tp_price = entry_price + sl_distance * RiskReward;
            } else {
                tp_price = entry_price - sl_distance * RiskReward;
            }
            break;

        case TP_ATR: {
            int atr_handle = iATR(_Symbol, PERIOD_CURRENT, ATR_Period);
            double atr_buffer[];
            ArraySetAsSeries(atr_buffer, true);
            CopyBuffer(atr_handle, 0, 1, 1, atr_buffer);
            double atr_value = atr_buffer[0];

            if(is_buy) {
                tp_price = entry_price + atr_value * ATR_Multiplier * RiskReward;
            } else {
                tp_price = entry_price - atr_value * ATR_Multiplier * RiskReward;
            }
            break;
        }
    }

    return tp_price;
}

//+------------------------------------------------------------------+
//| Calculate Lot Size                                               |
//+------------------------------------------------------------------+
double CalculateLotSize(double entry_price, double sl_price) {
    if(RiskPercent <= 0) return FixedLotSize;

    double balance = account.Balance();
    double risk_amount = balance * RiskPercent / 100.0;
    double sl_distance = MathAbs(entry_price - sl_price);

    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double lot_size = risk_amount / (sl_distance / tick_size * tick_value);

    // Normalize lot size
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lot_size = MathMax(min_lot, MathMin(max_lot, lot_size));
    lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;

    return lot_size;
}

//+------------------------------------------------------------------+
//| Manage existing trades                                           |
//+------------------------------------------------------------------+
void ManageTrades() {
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(position.SelectByIndex(i)) {
            if(position.Symbol() != _Symbol || position.Magic() != magic_number) continue;

            // Breakeven management
            if(UseBreakeven) {
                ManageBreakeven();
            }

            // Trailing stop management
            if(UseTrailing) {
                ManageTrailingStop();
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Manage breakeven                                                 |
//+------------------------------------------------------------------+
void ManageBreakeven() {
    double entry_price = position.PriceOpen();
    double current_price = (position.PositionType() == POSITION_TYPE_BUY) ?
                          SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                          SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    if(position.PositionType() == POSITION_TYPE_BUY) {
        if(current_price >= entry_price + BreakevenPoints * _Point) {
            if(position.StopLoss() < entry_price) {
                trade.PositionModify(position.Ticket(), entry_price, position.TakeProfit());
            }
        }
    } else {
        if(current_price <= entry_price - BreakevenPoints * _Point) {
            if(position.StopLoss() > entry_price) {
                trade.PositionModify(position.Ticket(), entry_price, position.TakeProfit());
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Manage trailing stop                                             |
//+------------------------------------------------------------------+
void ManageTrailingStop() {
    double entry_price = position.PriceOpen();
    double current_price = (position.PositionType() == POSITION_TYPE_BUY) ?
                          SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                          SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    if(position.PositionType() == POSITION_TYPE_BUY) {
        if(current_price >= entry_price + TrailingStart * _Point) {
            double new_sl = current_price - TrailingStep * _Point;
            if(new_sl > position.StopLoss()) {
                trade.PositionModify(position.Ticket(), new_sl, position.TakeProfit());
            }
        }
    } else {
        if(current_price <= entry_price - TrailingStart * _Point) {
            double new_sl = current_price + TrailingStep * _Point;
            if(new_sl < position.StopLoss()) {
                trade.PositionModify(position.Ticket(), new_sl, position.TakeProfit());
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Draw Order Block zones                                           |
//+------------------------------------------------------------------+
void DrawOrderBlocks() {
    // Clear existing OB objects
    ObjectsDeleteAll(0, ea_name + "_OB");

    // Draw bullish OBs
    for(int i = 0; i < ArraySize(bullish_obs); i++) {
        if(!bullish_obs[i].is_valid) continue;

        string obj_name = ea_name + "_OB_Bull_" + IntegerToString(i);
        ObjectCreate(0, obj_name, OBJ_RECTANGLE, 0,
                    bullish_obs[i].time, bullish_obs[i].high,
                    TimeCurrent() + PeriodSeconds(PERIOD_H1), bullish_obs[i].low);
        ObjectSetInteger(0, obj_name, OBJPROP_COLOR, BullishOBColor);
        ObjectSetInteger(0, obj_name, OBJPROP_STYLE, STYLE_SOLID);
        ObjectSetInteger(0, obj_name, OBJPROP_WIDTH, 1);
        ObjectSetInteger(0, obj_name, OBJPROP_FILL, true);
        ObjectSetInteger(0, obj_name, OBJPROP_BACK, true);
    }

    // Draw bearish OBs
    for(int i = 0; i < ArraySize(bearish_obs); i++) {
        if(!bearish_obs[i].is_valid) continue;

        string obj_name = ea_name + "_OB_Bear_" + IntegerToString(i);
        ObjectCreate(0, obj_name, OBJ_RECTANGLE, 0,
                    bearish_obs[i].time, bearish_obs[i].high,
                    TimeCurrent() + PeriodSeconds(PERIOD_H1), bearish_obs[i].low);
        ObjectSetInteger(0, obj_name, OBJPROP_COLOR, BearishOBColor);
        ObjectSetInteger(0, obj_name, OBJPROP_STYLE, STYLE_SOLID);
        ObjectSetInteger(0, obj_name, OBJPROP_WIDTH, 1);
        ObjectSetInteger(0, obj_name, OBJPROP_FILL, true);
        ObjectSetInteger(0, obj_name, OBJPROP_BACK, true);
    }
}

//+------------------------------------------------------------------+
//| Create dashboard                                                 |
//+------------------------------------------------------------------+
void CreateDashboard() {
    int x_start = 20;
    int y_start = 30;
    int line_height = 20;

    // Background panel
    string panel_name = ea_name + "_Panel";
    ObjectCreate(0, panel_name, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, panel_name, OBJPROP_XDISTANCE, x_start - 5);
    ObjectSetInteger(0, panel_name, OBJPROP_YDISTANCE, y_start - 5);
    ObjectSetInteger(0, panel_name, OBJPROP_XSIZE, 250);
    ObjectSetInteger(0, panel_name, OBJPROP_YSIZE, 150);
    ObjectSetInteger(0, panel_name, OBJPROP_COLOR, clrDarkBlue);
    ObjectSetInteger(0, panel_name, OBJPROP_BGCOLOR, clrNavy);
    ObjectSetInteger(0, panel_name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, panel_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);

    // Title
    string title_name = ea_name + "_Title";
    ObjectCreate(0, title_name, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, title_name, OBJPROP_XDISTANCE, x_start);
    ObjectSetInteger(0, title_name, OBJPROP_YDISTANCE, y_start);
    ObjectSetString(0, title_name, OBJPROP_TEXT, "Pressure OB EA v1.0");
    ObjectSetInteger(0, title_name, OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, title_name, OBJPROP_FONTSIZE, 10);
    ObjectSetString(0, title_name, OBJPROP_FONT, "Arial Bold");
}

//+------------------------------------------------------------------+
//| Update dashboard                                                 |
//+------------------------------------------------------------------+
void UpdateDashboard() {
    int x_start = 20;
    int y_start = 50;
    int line_height = 15;
    int current_y = y_start;

    // Trades today
    string trades_label = ea_name + "_TradesLabel";
    if(ObjectFind(0, trades_label) < 0) {
        ObjectCreate(0, trades_label, OBJ_LABEL, 0, 0, 0);
        ObjectSetInteger(0, trades_label, OBJPROP_XDISTANCE, x_start);
        ObjectSetInteger(0, trades_label, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(0, trades_label, OBJPROP_FONTSIZE, 8);
    }
    ObjectSetInteger(0, trades_label, OBJPROP_YDISTANCE, current_y);
    ObjectSetString(0, trades_label, OBJPROP_TEXT, "Trades Today: " + IntegerToString(trades_today) + "/" + IntegerToString(MaxTradesPerDay));
    current_y += line_height;

    // Current pressure
    int pressure = DetectPressure();
    string pressure_text = "No Pressure";
    color pressure_color = clrGray;
    if(pressure == 1) {
        pressure_text = "Bullish Pressure";
        pressure_color = clrLime;
    } else if(pressure == -1) {
        pressure_text = "Bearish Pressure";
        pressure_color = clrRed;
    }

    string pressure_label = ea_name + "_PressureLabel";
    if(ObjectFind(0, pressure_label) < 0) {
        ObjectCreate(0, pressure_label, OBJ_LABEL, 0, 0, 0);
        ObjectSetInteger(0, pressure_label, OBJPROP_XDISTANCE, x_start);
        ObjectSetInteger(0, pressure_label, OBJPROP_FONTSIZE, 8);
    }
    ObjectSetInteger(0, pressure_label, OBJPROP_YDISTANCE, current_y);
    ObjectSetString(0, pressure_label, OBJPROP_TEXT, "Pressure: " + pressure_text);
    ObjectSetInteger(0, pressure_label, OBJPROP_COLOR, pressure_color);
    current_y += line_height;

    // Order Blocks count
    string ob_label = ea_name + "_OBLabel";
    if(ObjectFind(0, ob_label) < 0) {
        ObjectCreate(0, ob_label, OBJ_LABEL, 0, 0, 0);
        ObjectSetInteger(0, ob_label, OBJPROP_XDISTANCE, x_start);
        ObjectSetInteger(0, ob_label, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(0, ob_label, OBJPROP_FONTSIZE, 8);
    }
    ObjectSetInteger(0, ob_label, OBJPROP_YDISTANCE, current_y);
    ObjectSetString(0, ob_label, OBJPROP_TEXT, "OBs: Bull=" + IntegerToString(ArraySize(bullish_obs)) + " Bear=" + IntegerToString(ArraySize(bearish_obs)));
    current_y += line_height;

    // Current time
    string time_label = ea_name + "_TimeLabel";
    if(ObjectFind(0, time_label) < 0) {
        ObjectCreate(0, time_label, OBJ_LABEL, 0, 0, 0);
        ObjectSetInteger(0, time_label, OBJPROP_XDISTANCE, x_start);
        ObjectSetInteger(0, time_label, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(0, time_label, OBJPROP_FONTSIZE, 8);
    }
    ObjectSetInteger(0, time_label, OBJPROP_YDISTANCE, current_y);
    ObjectSetString(0, time_label, OBJPROP_TEXT, "Time: " + TimeToString(TimeCurrent(), TIME_MINUTES));
    current_y += line_height;

    // Trading status
    string status_text = IsTradeTime() ? "ACTIVE" : "INACTIVE";
    color status_color = IsTradeTime() ? clrLime : clrRed;

    string status_label = ea_name + "_StatusLabel";
    if(ObjectFind(0, status_label) < 0) {
        ObjectCreate(0, status_label, OBJ_LABEL, 0, 0, 0);
        ObjectSetInteger(0, status_label, OBJPROP_XDISTANCE, x_start);
        ObjectSetInteger(0, status_label, OBJPROP_FONTSIZE, 8);
    }
    ObjectSetInteger(0, status_label, OBJPROP_YDISTANCE, current_y);
    ObjectSetString(0, status_label, OBJPROP_TEXT, "Status: " + status_text);
    ObjectSetInteger(0, status_label, OBJPROP_COLOR, status_color);
}
    