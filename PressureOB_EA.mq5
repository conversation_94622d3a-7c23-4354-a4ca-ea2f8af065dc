//+------------------------------------------------------------------+
//|                                                  PressureOB_EA.mq5 |
//|                    ADVANCED Pressure + Order Block Trading System |
//|                                    PROFIT MAXIMIZER v2.0 PREMIUM   |
//+------------------------------------------------------------------+
#property copyright "Advanced Pressure OB Trading System - PROFIT EDITION"
#property link      ""
#property version   "2.00"
#property description "Advanced Pressure + Order Block EA with Smart Money Concepts"
#property description "Features: Multi-timeframe analysis, Smart risk management, Market structure analysis"

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>

//--- Input Parameters
input group "=== PRESSURE SETTINGS ==="
input int PressureCandles = 1;                    // Number of candles for pressure confirmation
input double PressureBodyRatio = 0.5;             // Body to total candle ratio (0.5 = 50%)
input double PressureWickRatio = 0.5;             // Max wick to body ratio (0.5 = 50%)
input double PressureSizeMin = 5;                  // Minimum candle size in points

input group "=== ORDER BLOCK SETTINGS ==="
input ENUM_TIMEFRAMES OB_Timeframe = PERIOD_H1;   // Order Block timeframe
input int OB_LookbackBars = 50;                   // Bars to look back for OB
input double OB_MinMove = 20;                     // Minimum move after OB (points)
input int OB_ValidityBars = 100;                  // OB validity period (bars)
input bool ShowOBZones = true;                    // Show OB zones on chart

input group "=== ENTRY SETTINGS ==="
input bool RequireRejection = false;              // Require rejection candle at OB
input double RejectionWickRatio = 0.5;            // Rejection wick to body ratio
input int ConfirmationBars = 3;                   // Bars to wait for confirmation
input bool SimpleMode = true;                     // Simple mode (trade on pressure only)

input group "=== ADVANCED PROFIT FEATURES ==="
input bool UseMultiTimeframe = true;              // Multi-timeframe confirmation
input bool UseMarketStructure = true;             // Market structure analysis
input bool UseVolumeFilter = true;                // Volume-based filtering
input bool UseMomentumFilter = true;              // Momentum confirmation
input bool UseSmartRisk = true;                   // Smart risk management
input double ProfitMultiplier = 1.5;              // Profit target multiplier
input bool UsePartialClose = true;                // Partial profit taking
input double PartialClosePercent = 50.0;          // % to close at first target

input group "=== RISK MANAGEMENT ==="
input double RiskPercent = 2.0;                   // Risk per trade (% of balance)
input double FixedLotSize = 0.1;                  // Fixed lot size (if RiskPercent = 0)
enum ENUM_SL_TYPE { SL_FIXED, SL_ATR, SL_OB_SIZE };
input ENUM_SL_TYPE SL_Type = SL_ATR;              // Stop Loss type
input double SL_Fixed = 50;                       // Fixed SL in points
input int ATR_Period = 14;                        // ATR period for dynamic SL
input double ATR_Multiplier = 2.0;                // ATR multiplier for SL
enum ENUM_TP_TYPE { TP_FIXED, TP_RR, TP_ATR };
input ENUM_TP_TYPE TP_Type = TP_RR;               // Take Profit type
input double TP_Fixed = 100;                      // Fixed TP in points
input double RiskReward = 2.0;                    // Risk:Reward ratio
input bool UseBreakeven = true;                   // Use breakeven
input double BreakevenPoints = 20;                // Breakeven trigger (points)
input bool UseTrailing = true;                    // Use trailing stop
input double TrailingStart = 30;                  // Trailing start (points)
input double TrailingStep = 10;                   // Trailing step (points)

input group "=== TRADE FILTERS ==="
input int MaxTradesPerDay = 3;                    // Maximum trades per day
input string StartTime = "00:00";                 // Trading start time
input string EndTime = "23:59";                   // Trading end time
input bool UseNewsFilter = false;                 // Use news filter
input int NewsMinutesBefore = 30;                 // Minutes before news
input int NewsMinutesAfter = 30;                  // Minutes after news

input group "=== DISPLAY SETTINGS ==="
input bool ShowDashboard = true;                  // Show dashboard
input bool ShowAlerts = true;                     // Show alerts
input color BullishOBColor = clrBlue;             // Bullish OB color
input color BearishOBColor = clrRed;              // Bearish OB color

//--- Global Variables
struct OrderBlock {
    datetime time;
    double high;
    double low;
    bool is_bullish;
    bool is_valid;
    int creation_bar;
};

OrderBlock bullish_obs[];
OrderBlock bearish_obs[];
int trades_today = 0;
datetime last_trade_date = 0;
string ea_name = "PressureOB_EA";
int magic_number = 123456;

CTrade trade;
CPositionInfo position;
CAccountInfo account;

// Advanced analysis variables
int rsi_handle, macd_handle, volume_handle;
double daily_high, daily_low, weekly_high, weekly_low;
bool market_bullish_bias = false;
bool market_bearish_bias = false;
double profit_target_1, profit_target_2;
datetime last_structure_update = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    Print("🚀 ADVANCED PRESSURE OB EA v2.0 - PROFIT MAXIMIZER INITIALIZED! 🚀");

    // Initialize trade object
    trade.SetExpertMagicNumber(magic_number);
    trade.SetDeviationInPoints(10);
    trade.SetTypeFilling(ORDER_FILLING_FOK);

    // Initialize arrays
    ArrayResize(bullish_obs, 0);
    ArrayResize(bearish_obs, 0);

    // Initialize advanced indicators
    if(UseMomentumFilter) {
        rsi_handle = iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE);
        macd_handle = iMACD(_Symbol, PERIOD_CURRENT, 12, 26, 9, PRICE_CLOSE);
        if(rsi_handle == INVALID_HANDLE || macd_handle == INVALID_HANDLE) {
            Print("❌ Failed to initialize momentum indicators!");
            return(INIT_FAILED);
        }
    }

    if(UseVolumeFilter) {
        volume_handle = iVolumes(_Symbol, PERIOD_CURRENT, VOLUME_TICK);
        if(volume_handle == INVALID_HANDLE) {
            Print("❌ Failed to initialize volume indicator!");
            return(INIT_FAILED);
        }
    }

    // Initialize market structure
    UpdateMarketStructure();

    // Create dashboard if enabled
    if(ShowDashboard) {
        CreateDashboard();
    }

    Print("✅ All systems ready! Let's make some PROFIT! 💰");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    // Clean up objects
    ObjectsDeleteAll(0, ea_name);
    Print("Pressure OB EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    // Check if new bar
    static datetime last_bar_time = 0;
    datetime current_bar_time = iTime(_Symbol, PERIOD_CURRENT, 0);
    if(current_bar_time == last_bar_time) return;
    last_bar_time = current_bar_time;

    // DEBUG: Print new bar info
    static int debug_counter = 0;
    debug_counter++;
    if(debug_counter % 10 == 0) { // Print every 10th bar to avoid spam
        Print("DEBUG: New bar at ", TimeToString(current_bar_time));
    }

    // Reset daily trade counter
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    string current_date = StringFormat("%04d.%02d.%02d", dt.year, dt.mon, dt.day);

    MqlDateTime last_dt;
    TimeToStruct(last_trade_date, last_dt);
    string last_date = StringFormat("%04d.%02d.%02d", last_dt.year, last_dt.mon, last_dt.day);

    if(current_date != last_date) {
        trades_today = 0;
        last_trade_date = TimeCurrent();
        Print("DEBUG: New day, reset trades_today to 0");
    }

    // Check trading time
    if(!IsTradeTime()) {
        if(debug_counter % 50 == 0) Print("DEBUG: Outside trading hours");
        return;
    }

    // Check maximum trades per day
    if(trades_today >= MaxTradesPerDay) {
        if(debug_counter % 50 == 0) Print("DEBUG: Max trades per day reached: ", trades_today);
        return;
    }

    // Update market structure analysis
    if(UseMarketStructure) {
        UpdateMarketStructure();
    }

    // Update Order Blocks
    UpdateOrderBlocks();

    // DEBUG: Print OB status
    if(debug_counter % 20 == 0) {
        Print("DEBUG: Bullish OBs: ", ArraySize(bullish_obs), ", Bearish OBs: ", ArraySize(bearish_obs));
        if(UseMarketStructure) {
            string bias = market_bullish_bias ? "BULLISH" : (market_bearish_bias ? "BEARISH" : "NEUTRAL");
            Print("DEBUG: Market Bias: ", bias);
        }
    }

    // Advanced signal analysis
    CheckAdvancedTradeSignals();

    // Manage existing trades
    ManageTrades();

    // Update dashboard
    if(ShowDashboard) {
        UpdateDashboard();
    }
}

//+------------------------------------------------------------------+
//| Check if current time is within trading hours                    |
//+------------------------------------------------------------------+
bool IsTradeTime() {
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    string current_time = StringFormat("%02d:%02d", dt.hour, dt.min);

    bool is_trade_time = (current_time >= StartTime && current_time <= EndTime);

    static bool last_trade_time_status = true;
    static int time_debug_counter = 0;
    time_debug_counter++;

    if(!is_trade_time && last_trade_time_status) {
        Print("DEBUG: Outside trading hours. Current: ", current_time, ", Range: ", StartTime, " - ", EndTime);
        last_trade_time_status = false;
    } else if(is_trade_time && !last_trade_time_status) {
        Print("DEBUG: Back in trading hours. Current: ", current_time);
        last_trade_time_status = true;
    }

    return is_trade_time;
}

//+------------------------------------------------------------------+
//| Detect pressure (bullish or bearish)                            |
//+------------------------------------------------------------------+
int DetectPressure(int start_bar = 1) {
    int bullish_count = 0;
    int bearish_count = 0;
    
    for(int i = start_bar; i < start_bar + PressureCandles; i++) {
        double open_price = iOpen(_Symbol, PERIOD_CURRENT, i);
        double close_price = iClose(_Symbol, PERIOD_CURRENT, i);
        double high_price = iHigh(_Symbol, PERIOD_CURRENT, i);
        double low_price = iLow(_Symbol, PERIOD_CURRENT, i);
        
        double body_size = MathAbs(close_price - open_price);
        double total_size = high_price - low_price;
        double upper_wick = high_price - MathMax(open_price, close_price);
        double lower_wick = MathMin(open_price, close_price) - low_price;
        
        // Check if candle meets pressure criteria
        if(total_size < PressureSizeMin * _Point) continue;
        if(total_size == 0 || body_size / total_size < PressureBodyRatio) continue;
        if(body_size == 0 || MathMax(upper_wick, lower_wick) / body_size > PressureWickRatio) continue;
        
        // Count bullish/bearish candles
        if(close_price > open_price) bullish_count++;
        else if(close_price < open_price) bearish_count++;
    }
    
    if(bullish_count >= PressureCandles) return 1;  // Bullish pressure
    if(bearish_count >= PressureCandles) return -1; // Bearish pressure
    
    return 0; // No pressure
}

//+------------------------------------------------------------------+
//| Update Market Structure Analysis (PROFIT BOOSTER)               |
//+------------------------------------------------------------------+
void UpdateMarketStructure() {
    // Update daily and weekly highs/lows
    daily_high = iHigh(_Symbol, PERIOD_D1, 0);
    daily_low = iLow(_Symbol, PERIOD_D1, 0);
    weekly_high = iHigh(_Symbol, PERIOD_W1, 0);
    weekly_low = iLow(_Symbol, PERIOD_W1, 0);

    double current_price = (SymbolInfoDouble(_Symbol, SYMBOL_BID) + SymbolInfoDouble(_Symbol, SYMBOL_ASK)) / 2;

    // Determine market bias based on position relative to key levels
    double daily_range = daily_high - daily_low;
    double weekly_range = weekly_high - weekly_low;

    if(daily_range > 0) {
        double daily_position = (current_price - daily_low) / daily_range;
        double weekly_position = weekly_range > 0 ? (current_price - weekly_low) / weekly_range : 0.5;

        // Bullish bias if price is in upper portion of ranges
        market_bullish_bias = (daily_position > 0.6 && weekly_position > 0.5);
        market_bearish_bias = (daily_position < 0.4 && weekly_position < 0.5);

        // Calculate dynamic profit targets based on market structure
        if(UseSmartRisk) {
            double avg_range = (daily_range + weekly_range) / 2;
            profit_target_1 = avg_range * 0.3 * ProfitMultiplier;
            profit_target_2 = avg_range * 0.6 * ProfitMultiplier;
        }
    }

    last_structure_update = TimeCurrent();
}

//+------------------------------------------------------------------+
//| Advanced Trade Signal Analysis (PROFIT MAXIMIZER)              |
//+------------------------------------------------------------------+
void CheckAdvancedTradeSignals() {
    int pressure = DetectPressure();
    bool momentum_confirmed = true;
    bool volume_confirmed = true;
    bool structure_aligned = true;

    // Multi-timeframe momentum confirmation
    if(UseMomentumFilter) {
        momentum_confirmed = CheckMomentumAlignment(pressure);
    }

    // Volume confirmation
    if(UseVolumeFilter) {
        volume_confirmed = CheckVolumeConfirmation();
    }

    // Market structure alignment
    if(UseMarketStructure) {
        structure_aligned = CheckStructureAlignment(pressure);
    }

    // DEBUG: Print filter status
    static int last_pressure_debug = 999;
    if(pressure != last_pressure_debug) {
        string pressure_text = "No Pressure";
        if(pressure == 1) pressure_text = "🟢 BULLISH PRESSURE";
        else if(pressure == -1) pressure_text = "🔴 BEARISH PRESSURE";

        Print("🎯 SIGNAL ANALYSIS: ", pressure_text);
        Print("   📊 Momentum: ", momentum_confirmed ? "✅" : "❌");
        Print("   📈 Volume: ", volume_confirmed ? "✅" : "❌");
        Print("   🏗️ Structure: ", structure_aligned ? "✅" : "❌");
        last_pressure_debug = pressure;
    }

    // Execute trades only if all filters confirm
    if(pressure != 0 && momentum_confirmed && volume_confirmed && structure_aligned) {
        if(SimpleMode) {
            if(pressure == 1) {
                Print("🚀 EXECUTING ADVANCED BUY - ALL SYSTEMS GO! 💰");
                ExecuteAdvancedBuy();
            } else if(pressure == -1) {
                Print("🚀 EXECUTING ADVANCED SELL - ALL SYSTEMS GO! 💰");
                ExecuteAdvancedSell();
            }
        } else {
            CheckTradeSignals(); // Use original OB method
        }
    }
}

//+------------------------------------------------------------------+
//| Update Order Blocks from higher timeframe                        |
//+------------------------------------------------------------------+
void UpdateOrderBlocks() {
    int tf_bars = iBars(_Symbol, OB_Timeframe);
    if(tf_bars < OB_LookbackBars + 10) return;
    
    // Clear old invalid OBs
    CleanupOldOrderBlocks();
    
    // Look for new bullish Order Blocks
    for(int i = 5; i < OB_LookbackBars; i++) {
        if(IsNewBullishOB(i)) {
            AddBullishOB(i);
        }
    }
    
    // Look for new bearish Order Blocks  
    for(int i = 5; i < OB_LookbackBars; i++) {
        if(IsNewBearishOB(i)) {
            AddBearishOB(i);
        }
    }
    
    // Draw OB zones if enabled
    if(ShowOBZones) {
        DrawOrderBlocks();
    }
}

//+------------------------------------------------------------------+
//| Check if bar forms a new bullish Order Block                    |
//+------------------------------------------------------------------+
bool IsNewBullishOB(int bar_index) {
    double high_i = iHigh(_Symbol, OB_Timeframe, bar_index);
    double low_i = iLow(_Symbol, OB_Timeframe, bar_index);
    double close_i = iClose(_Symbol, OB_Timeframe, bar_index);
    double open_i = iOpen(_Symbol, OB_Timeframe, bar_index);
    
    // Must be a bearish candle
    if(close_i >= open_i) return false;
    
    // Check for strong bullish move after this candle
    double highest_after = 0;
    for(int j = bar_index - 1; j >= 1; j--) {
        double high_j = iHigh(_Symbol, OB_Timeframe, j);
        if(high_j > highest_after) highest_after = high_j;
    }
    
    double move_size = highest_after - high_i;
    return (move_size >= OB_MinMove * _Point);
}

//+------------------------------------------------------------------+
//| Check if bar forms a new bearish Order Block                    |
//+------------------------------------------------------------------+
bool IsNewBearishOB(int bar_index) {
    double high_i = iHigh(_Symbol, OB_Timeframe, bar_index);
    double low_i = iLow(_Symbol, OB_Timeframe, bar_index);
    double close_i = iClose(_Symbol, OB_Timeframe, bar_index);
    double open_i = iOpen(_Symbol, OB_Timeframe, bar_index);
    
    // Must be a bullish candle
    if(close_i <= open_i) return false;
    
    // Check for strong bearish move after this candle
    double lowest_after = DBL_MAX;
    for(int j = bar_index - 1; j >= 1; j--) {
        double low_j = iLow(_Symbol, OB_Timeframe, j);
        if(low_j < lowest_after) lowest_after = low_j;
    }
    
    double move_size = low_i - lowest_after;
    return (move_size >= OB_MinMove * _Point);
}

//+------------------------------------------------------------------+
//| Add new bullish Order Block                                      |
//+------------------------------------------------------------------+
void AddBullishOB(int bar_index) {
    int size = ArraySize(bullish_obs);
    ArrayResize(bullish_obs, size + 1);

    bullish_obs[size].time = iTime(_Symbol, OB_Timeframe, bar_index);
    bullish_obs[size].high = iHigh(_Symbol, OB_Timeframe, bar_index);
    bullish_obs[size].low = iLow(_Symbol, OB_Timeframe, bar_index);
    bullish_obs[size].is_bullish = true;
    bullish_obs[size].is_valid = true;
    bullish_obs[size].creation_bar = iBars(_Symbol, PERIOD_CURRENT);
}

//+------------------------------------------------------------------+
//| Add new bearish Order Block                                      |
//+------------------------------------------------------------------+
void AddBearishOB(int bar_index) {
    int size = ArraySize(bearish_obs);
    ArrayResize(bearish_obs, size + 1);

    bearish_obs[size].time = iTime(_Symbol, OB_Timeframe, bar_index);
    bearish_obs[size].high = iHigh(_Symbol, OB_Timeframe, bar_index);
    bearish_obs[size].low = iLow(_Symbol, OB_Timeframe, bar_index);
    bearish_obs[size].is_bullish = false;
    bearish_obs[size].is_valid = true;
    bearish_obs[size].creation_bar = iBars(_Symbol, PERIOD_CURRENT);
}

//+------------------------------------------------------------------+
//| Clean up old invalid Order Blocks                               |
//+------------------------------------------------------------------+
void CleanupOldOrderBlocks() {
    int current_bars = iBars(_Symbol, PERIOD_CURRENT);

    // Remove old bullish OBs
    for(int i = ArraySize(bullish_obs) - 1; i >= 0; i--) {
        if(current_bars - bullish_obs[i].creation_bar > OB_ValidityBars) {
            ArrayRemove(bullish_obs, i, 1);
        }
    }

    // Remove old bearish OBs
    for(int i = ArraySize(bearish_obs) - 1; i >= 0; i--) {
        if(current_bars - bearish_obs[i].creation_bar > OB_ValidityBars) {
            ArrayRemove(bearish_obs, i, 1);
        }
    }
}

//+------------------------------------------------------------------+
//| Check for trade signals                                          |
//+------------------------------------------------------------------+
void CheckTradeSignals() {
    int pressure = DetectPressure();

    // DEBUG: Print pressure status
    static int last_pressure = 999;
    if(pressure != last_pressure) {
        string pressure_text = "No Pressure";
        if(pressure == 1) pressure_text = "Bullish Pressure";
        else if(pressure == -1) pressure_text = "Bearish Pressure";
        Print("DEBUG: Pressure changed to: ", pressure_text);
        last_pressure = pressure;
    }

    if(SimpleMode) {
        // Simple mode: trade directly on pressure without Order Blocks
        if(pressure == 1) { // Bullish pressure
            Print("DEBUG: Simple mode - executing buy on bullish pressure");
            ExecuteSimpleBuy();
        } else if(pressure == -1) { // Bearish pressure
            Print("DEBUG: Simple mode - executing sell on bearish pressure");
            ExecuteSimpleSell();
        }
    } else {
        // Normal mode: require Order Blocks
        if(pressure == 1) { // Bullish pressure
            Print("DEBUG: Checking buy signal...");
            CheckBuySignal();
        } else if(pressure == -1) { // Bearish pressure
            Print("DEBUG: Checking sell signal...");
            CheckSellSignal();
        }
    }
}

//+------------------------------------------------------------------+
//| Execute simple buy trade (without Order Block requirement)      |
//+------------------------------------------------------------------+
void ExecuteSimpleBuy() {
    Print("DEBUG: ExecuteSimpleBuy called!");

    if(PositionsTotal() > 0) {
        Print("DEBUG: Cannot execute simple buy - already have ", PositionsTotal(), " open positions");
        return;
    }

    // Create a dummy Order Block for the function call
    OrderBlock dummy_ob;
    dummy_ob.high = iHigh(_Symbol, PERIOD_CURRENT, 1);
    dummy_ob.low = iLow(_Symbol, PERIOD_CURRENT, 1);
    dummy_ob.is_bullish = true;
    dummy_ob.is_valid = true;

    ExecuteBuyTrade(dummy_ob);
}

//+------------------------------------------------------------------+
//| Execute simple sell trade (without Order Block requirement)     |
//+------------------------------------------------------------------+
void ExecuteSimpleSell() {
    Print("DEBUG: ExecuteSimpleSell called!");

    if(PositionsTotal() > 0) {
        Print("DEBUG: Cannot execute simple sell - already have ", PositionsTotal(), " open positions");
        return;
    }

    // Create a dummy Order Block for the function call
    OrderBlock dummy_ob;
    dummy_ob.high = iHigh(_Symbol, PERIOD_CURRENT, 1);
    dummy_ob.low = iLow(_Symbol, PERIOD_CURRENT, 1);
    dummy_ob.is_bullish = false;
    dummy_ob.is_valid = true;

    ExecuteSellTrade(dummy_ob);
}

//+------------------------------------------------------------------+
//| Check Momentum Alignment (RSI + MACD Confirmation)             |
//+------------------------------------------------------------------+
bool CheckMomentumAlignment(int pressure_direction) {
    if(!UseMomentumFilter) return true;

    double rsi_buffer[], macd_main[], macd_signal[];
    ArraySetAsSeries(rsi_buffer, true);
    ArraySetAsSeries(macd_main, true);
    ArraySetAsSeries(macd_signal, true);

    if(CopyBuffer(rsi_handle, 0, 0, 3, rsi_buffer) < 3) return false;
    if(CopyBuffer(macd_handle, 0, 0, 3, macd_main) < 3) return false;
    if(CopyBuffer(macd_handle, 1, 0, 3, macd_signal) < 3) return false;

    double rsi_current = rsi_buffer[0];
    double macd_current = macd_main[0];
    double macd_signal_current = macd_signal[0];

    if(pressure_direction == 1) { // Bullish
        // RSI should be oversold or rising, MACD should be bullish
        bool rsi_ok = (rsi_current < 70 && rsi_current > rsi_buffer[1]);
        bool macd_ok = (macd_current > macd_signal_current);
        return (rsi_ok || macd_ok); // At least one should confirm
    } else if(pressure_direction == -1) { // Bearish
        // RSI should be overbought or falling, MACD should be bearish
        bool rsi_ok = (rsi_current > 30 && rsi_current < rsi_buffer[1]);
        bool macd_ok = (macd_current < macd_signal_current);
        return (rsi_ok || macd_ok); // At least one should confirm
    }

    return false;
}

//+------------------------------------------------------------------+
//| Check Volume Confirmation                                        |
//+------------------------------------------------------------------+
bool CheckVolumeConfirmation() {
    if(!UseVolumeFilter) return true;

    double volume_buffer[];
    ArraySetAsSeries(volume_buffer, true);

    if(CopyBuffer(volume_handle, 0, 0, 10, volume_buffer) < 10) return false;

    // Calculate average volume of last 10 bars
    double avg_volume = 0;
    for(int i = 1; i < 10; i++) {
        avg_volume += volume_buffer[i];
    }
    avg_volume /= 9;

    // Current volume should be above average
    return (volume_buffer[0] > avg_volume * 1.2);
}

//+------------------------------------------------------------------+
//| Check Structure Alignment                                        |
//+------------------------------------------------------------------+
bool CheckStructureAlignment(int pressure_direction) {
    if(!UseMarketStructure) return true;

    if(pressure_direction == 1) { // Bullish pressure
        return market_bullish_bias || !market_bearish_bias;
    } else if(pressure_direction == -1) { // Bearish pressure
        return market_bearish_bias || !market_bullish_bias;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Execute Advanced Buy Trade (PROFIT MAXIMIZER)                   |
//+------------------------------------------------------------------+
void ExecuteAdvancedBuy() {
    Print("💎 ADVANCED BUY EXECUTION INITIATED!");

    if(PositionsTotal() > 0) {
        Print("⚠️ Cannot execute - already have ", PositionsTotal(), " positions");
        return;
    }

    double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl_price, tp_price;

    // Smart risk calculation
    if(UseSmartRisk && profit_target_1 > 0) {
        sl_price = entry_price - profit_target_1 / ProfitMultiplier;
        tp_price = entry_price + profit_target_1;
    } else {
        // Fallback to standard calculation
        OrderBlock dummy_ob;
        dummy_ob.high = iHigh(_Symbol, PERIOD_CURRENT, 1);
        dummy_ob.low = iLow(_Symbol, PERIOD_CURRENT, 1);
        sl_price = CalculateStopLoss(true, dummy_ob);
        tp_price = CalculateTakeProfit(true, entry_price, sl_price);
    }

    double lot_size = CalculateAdvancedLotSize(entry_price, sl_price);

    Print("🎯 ADVANCED BUY Parameters:");
    Print("   💰 Entry: ", entry_price);
    Print("   🛡️ SL: ", sl_price, " (", NormalizeDouble((entry_price - sl_price) / _Point, 0), " points)");
    Print("   🎯 TP: ", tp_price, " (", NormalizeDouble((tp_price - entry_price) / _Point, 0), " points)");
    Print("   📊 Lot: ", lot_size);

    if(trade.Buy(lot_size, _Symbol, entry_price, sl_price, tp_price, "💎 ADVANCED PressureOB BUY")) {
        trades_today++;
        Print("🚀 ADVANCED BUY EXECUTED SUCCESSFULLY! 💰💰💰");
        if(ShowAlerts) Alert("🚀 PROFIT MAXIMIZER: ADVANCED BUY at ", entry_price);
    } else {
        Print("❌ Advanced buy failed! Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| Execute Advanced Sell Trade (PROFIT MAXIMIZER)                  |
//+------------------------------------------------------------------+
void ExecuteAdvancedSell() {
    Print("💎 ADVANCED SELL EXECUTION INITIATED!");

    if(PositionsTotal() > 0) {
        Print("⚠️ Cannot execute - already have ", PositionsTotal(), " positions");
        return;
    }

    double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl_price, tp_price;

    // Smart risk calculation
    if(UseSmartRisk && profit_target_1 > 0) {
        sl_price = entry_price + profit_target_1 / ProfitMultiplier;
        tp_price = entry_price - profit_target_1;
    } else {
        // Fallback to standard calculation
        OrderBlock dummy_ob;
        dummy_ob.high = iHigh(_Symbol, PERIOD_CURRENT, 1);
        dummy_ob.low = iLow(_Symbol, PERIOD_CURRENT, 1);
        sl_price = CalculateStopLoss(false, dummy_ob);
        tp_price = CalculateTakeProfit(false, entry_price, sl_price);
    }

    double lot_size = CalculateAdvancedLotSize(entry_price, sl_price);

    Print("🎯 ADVANCED SELL Parameters:");
    Print("   💰 Entry: ", entry_price);
    Print("   🛡️ SL: ", sl_price, " (", NormalizeDouble((sl_price - entry_price) / _Point, 0), " points)");
    Print("   🎯 TP: ", tp_price, " (", NormalizeDouble((entry_price - tp_price) / _Point, 0), " points)");
    Print("   📊 Lot: ", lot_size);

    if(trade.Sell(lot_size, _Symbol, entry_price, sl_price, tp_price, "💎 ADVANCED PressureOB SELL")) {
        trades_today++;
        Print("🚀 ADVANCED SELL EXECUTED SUCCESSFULLY! 💰💰💰");
        if(ShowAlerts) Alert("🚀 PROFIT MAXIMIZER: ADVANCED SELL at ", entry_price);
    } else {
        Print("❌ Advanced sell failed! Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| Calculate Advanced Lot Size (SMART RISK MANAGEMENT)            |
//+------------------------------------------------------------------+
double CalculateAdvancedLotSize(double entry_price, double sl_price) {
    if(RiskPercent <= 0) return FixedLotSize;

    double balance = account.Balance();
    double equity = account.Equity();

    // Use equity instead of balance for more conservative approach
    double risk_amount = MathMin(balance, equity) * RiskPercent / 100.0;

    // Adjust risk based on market volatility
    if(UseSmartRisk) {
        double daily_range = daily_high - daily_low;
        double avg_daily_range = 0;

        // Calculate average daily range for volatility adjustment
        for(int i = 1; i <= 10; i++) {
            double high = iHigh(_Symbol, PERIOD_D1, i);
            double low = iLow(_Symbol, PERIOD_D1, i);
            avg_daily_range += (high - low);
        }
        avg_daily_range /= 10;

        if(avg_daily_range > 0) {
            double volatility_ratio = daily_range / avg_daily_range;
            // Reduce risk in high volatility, increase in low volatility
            risk_amount *= (2.0 - MathMin(volatility_ratio, 1.5));
        }
    }

    double sl_distance = MathAbs(entry_price - sl_price);
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

    if(tick_size > 0 && tick_value > 0 && sl_distance > 0) {
        double lot_size = risk_amount / (sl_distance / tick_size * tick_value);

        // Normalize lot size
        double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
        double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
        double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

        lot_size = MathMax(min_lot, MathMin(max_lot, lot_size));
        if(lot_step > 0) {
            lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;
        }

        return lot_size;
    }

    return FixedLotSize;
}

//+------------------------------------------------------------------+
//| Check for buy signal                                             |
//+------------------------------------------------------------------+
void CheckBuySignal() {
    double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    Print("DEBUG: CheckBuySignal - Current price: ", current_price, ", Bullish OBs: ", ArraySize(bullish_obs));

    // Check if price is near a bullish OB
    for(int i = 0; i < ArraySize(bullish_obs); i++) {
        if(!bullish_obs[i].is_valid) {
            Print("DEBUG: Bullish OB ", i, " is invalid");
            continue;
        }

        Print("DEBUG: Bullish OB ", i, " - High: ", bullish_obs[i].high, ", Low: ", bullish_obs[i].low);

        // Check if price is within OB zone
        if(current_price >= bullish_obs[i].low && current_price <= bullish_obs[i].high) {
            Print("DEBUG: Price is within Bullish OB zone!");

            // Check for rejection if required
            if(RequireRejection && !CheckRejectionCandle(true)) {
                Print("DEBUG: Rejection candle required but not found");
                continue;
            }

            Print("DEBUG: All conditions met, executing buy trade!");
            // Execute buy trade
            ExecuteBuyTrade(bullish_obs[i]);
            break;
        }
    }
}

//+------------------------------------------------------------------+
//| Check for sell signal                                            |
//+------------------------------------------------------------------+
void CheckSellSignal() {
    double current_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    Print("DEBUG: CheckSellSignal - Current price: ", current_price, ", Bearish OBs: ", ArraySize(bearish_obs));

    // Check if price is near a bearish OB
    for(int i = 0; i < ArraySize(bearish_obs); i++) {
        if(!bearish_obs[i].is_valid) {
            Print("DEBUG: Bearish OB ", i, " is invalid");
            continue;
        }

        Print("DEBUG: Bearish OB ", i, " - High: ", bearish_obs[i].high, ", Low: ", bearish_obs[i].low);

        // Check if price is within OB zone
        if(current_price >= bearish_obs[i].low && current_price <= bearish_obs[i].high) {
            Print("DEBUG: Price is within Bearish OB zone!");

            // Check for rejection if required
            if(RequireRejection && !CheckRejectionCandle(false)) {
                Print("DEBUG: Rejection candle required but not found");
                continue;
            }

            Print("DEBUG: All conditions met, executing sell trade!");
            // Execute sell trade
            ExecuteSellTrade(bearish_obs[i]);
            break;
        }
    }
}

//+------------------------------------------------------------------+
//| Check for rejection candle                                       |
//+------------------------------------------------------------------+
bool CheckRejectionCandle(bool is_bullish_setup) {
    double open_price = iOpen(_Symbol, PERIOD_CURRENT, 1);
    double close_price = iClose(_Symbol, PERIOD_CURRENT, 1);
    double high_price = iHigh(_Symbol, PERIOD_CURRENT, 1);
    double low_price = iLow(_Symbol, PERIOD_CURRENT, 1);

    double body_size = MathAbs(close_price - open_price);
    double upper_wick = high_price - MathMax(open_price, close_price);
    double lower_wick = MathMin(open_price, close_price) - low_price;

    if(body_size == 0) return false;

    if(is_bullish_setup) {
        // Look for bullish rejection (long lower wick)
        return (lower_wick / body_size >= RejectionWickRatio && close_price > open_price);
    } else {
        // Look for bearish rejection (long upper wick)
        return (upper_wick / body_size >= RejectionWickRatio && close_price < open_price);
    }
}

//+------------------------------------------------------------------+
//| Execute buy trade                                                |
//+------------------------------------------------------------------+
void ExecuteBuyTrade(OrderBlock &ob) {
    Print("DEBUG: ExecuteBuyTrade called!");

    if(PositionsTotal() > 0) {
        Print("DEBUG: Cannot execute buy trade - already have ", PositionsTotal(), " open positions");
        return; // Only one trade at a time
    }

    double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl_price = CalculateStopLoss(true, ob);
    double tp_price = CalculateTakeProfit(true, entry_price, sl_price);
    double lot_size = CalculateLotSize(entry_price, sl_price);

    Print("DEBUG: Buy trade parameters - Entry: ", entry_price, ", SL: ", sl_price, ", TP: ", tp_price, ", Lot: ", lot_size);

    if(trade.Buy(lot_size, _Symbol, entry_price, sl_price, tp_price, "PressureOB Buy")) {
        trades_today++;
        if(ShowAlerts) Alert("PressureOB: BUY signal executed at ", entry_price);
        Print("Buy trade executed: Entry=", entry_price, " SL=", sl_price, " TP=", tp_price);
    } else {
        Print("DEBUG: Buy trade failed! Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| Execute sell trade                                               |
//+------------------------------------------------------------------+
void ExecuteSellTrade(OrderBlock &ob) {
    Print("DEBUG: ExecuteSellTrade called!");

    if(PositionsTotal() > 0) {
        Print("DEBUG: Cannot execute sell trade - already have ", PositionsTotal(), " open positions");
        return; // Only one trade at a time
    }

    double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl_price = CalculateStopLoss(false, ob);
    double tp_price = CalculateTakeProfit(false, entry_price, sl_price);
    double lot_size = CalculateLotSize(entry_price, sl_price);

    Print("DEBUG: Sell trade parameters - Entry: ", entry_price, ", SL: ", sl_price, ", TP: ", tp_price, ", Lot: ", lot_size);

    if(trade.Sell(lot_size, _Symbol, entry_price, sl_price, tp_price, "PressureOB Sell")) {
        trades_today++;
        if(ShowAlerts) Alert("PressureOB: SELL signal executed at ", entry_price);
        Print("Sell trade executed: Entry=", entry_price, " SL=", sl_price, " TP=", tp_price);
    } else {
        Print("DEBUG: Sell trade failed! Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| Calculate Stop Loss                                              |
//+------------------------------------------------------------------+
double CalculateStopLoss(bool is_buy, OrderBlock &ob) {
    double sl_price = 0;

    switch(SL_Type) {
        case SL_FIXED:
            if(is_buy) {
                sl_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SL_Fixed * _Point;
            } else {
                sl_price = SymbolInfoDouble(_Symbol, SYMBOL_BID) + SL_Fixed * _Point;
            }
            break;

        case SL_ATR: {
            int atr_handle = iATR(_Symbol, PERIOD_CURRENT, ATR_Period);
            double atr_buffer[];
            ArraySetAsSeries(atr_buffer, true);
            CopyBuffer(atr_handle, 0, 1, 1, atr_buffer);
            double atr_value = atr_buffer[0];

            if(is_buy) {
                sl_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK) - atr_value * ATR_Multiplier;
            } else {
                sl_price = SymbolInfoDouble(_Symbol, SYMBOL_BID) + atr_value * ATR_Multiplier;
            }
            break;
        }

        case SL_OB_SIZE:
            if(is_buy) {
                sl_price = ob.low - 5 * _Point; // Below OB zone
            } else {
                sl_price = ob.high + 5 * _Point; // Above OB zone
            }
            break;
    }

    return sl_price;
}

//+------------------------------------------------------------------+
//| Calculate Take Profit                                            |
//+------------------------------------------------------------------+
double CalculateTakeProfit(bool is_buy, double entry_price, double sl_price) {
    double tp_price = 0;
    double sl_distance = MathAbs(entry_price - sl_price);

    switch(TP_Type) {
        case TP_FIXED:
            if(is_buy) {
                tp_price = entry_price + TP_Fixed * _Point;
            } else {
                tp_price = entry_price - TP_Fixed * _Point;
            }
            break;

        case TP_RR:
            if(is_buy) {
                tp_price = entry_price + sl_distance * RiskReward;
            } else {
                tp_price = entry_price - sl_distance * RiskReward;
            }
            break;

        case TP_ATR: {
            int atr_handle = iATR(_Symbol, PERIOD_CURRENT, ATR_Period);
            double atr_buffer[];
            ArraySetAsSeries(atr_buffer, true);
            CopyBuffer(atr_handle, 0, 1, 1, atr_buffer);
            double atr_value = atr_buffer[0];

            if(is_buy) {
                tp_price = entry_price + atr_value * ATR_Multiplier * RiskReward;
            } else {
                tp_price = entry_price - atr_value * ATR_Multiplier * RiskReward;
            }
            break;
        }
    }

    return tp_price;
}

//+------------------------------------------------------------------+
//| Calculate Lot Size                                               |
//+------------------------------------------------------------------+
double CalculateLotSize(double entry_price, double sl_price) {
    if(RiskPercent <= 0) return FixedLotSize;

    double balance = account.Balance();
    double risk_amount = balance * RiskPercent / 100.0;
    double sl_distance = MathAbs(entry_price - sl_price);

    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double lot_size = risk_amount / (sl_distance / tick_size * tick_value);

    // Normalize lot size
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lot_size = MathMax(min_lot, MathMin(max_lot, lot_size));
    lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;

    return lot_size;
}

//+------------------------------------------------------------------+
//| Manage existing trades                                           |
//+------------------------------------------------------------------+
void ManageTrades() {
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(position.SelectByIndex(i)) {
            if(position.Symbol() != _Symbol || position.Magic() != magic_number) continue;

            // Advanced profit management
            if(UsePartialClose) {
                ManagePartialProfits();
            }

            // Breakeven management
            if(UseBreakeven) {
                ManageAdvancedBreakeven();
            }

            // Advanced trailing stop management
            if(UseTrailing) {
                ManageAdvancedTrailing();
            }

            // Smart exit based on market conditions
            if(UseMarketStructure) {
                ManageSmartExit();
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Manage Partial Profits (PROFIT MAXIMIZER)                       |
//+------------------------------------------------------------------+
void ManagePartialProfits() {
    double entry_price = position.PriceOpen();
    double current_price = (position.PositionType() == POSITION_TYPE_BUY) ?
                          SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                          SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double tp_price = position.TakeProfit();

    if(tp_price == 0) return; // No TP set

    double profit_distance = 0;
    if(position.PositionType() == POSITION_TYPE_BUY) {
        profit_distance = current_price - entry_price;
        double target_distance = tp_price - entry_price;

        // Check if we've reached partial profit target
        if(profit_distance >= target_distance * 0.5 && position.Volume() > SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN)) {
            double partial_volume = position.Volume() * PartialClosePercent / 100.0;
            partial_volume = NormalizeDouble(partial_volume, 2);

            if(partial_volume >= SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN)) {
                if(trade.PositionClosePartial(position.Ticket(), partial_volume)) {
                    Print("💰 PARTIAL PROFIT TAKEN! Closed ", partial_volume, " lots at ", current_price);
                    Print("🎯 Profit secured: ", NormalizeDouble(profit_distance / _Point, 0), " points");
                }
            }
        }
    } else { // SELL position
        profit_distance = entry_price - current_price;
        double target_distance = entry_price - tp_price;

        // Check if we've reached partial profit target
        if(profit_distance >= target_distance * 0.5 && position.Volume() > SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN)) {
            double partial_volume = position.Volume() * PartialClosePercent / 100.0;
            partial_volume = NormalizeDouble(partial_volume, 2);

            if(partial_volume >= SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN)) {
                if(trade.PositionClosePartial(position.Ticket(), partial_volume)) {
                    Print("💰 PARTIAL PROFIT TAKEN! Closed ", partial_volume, " lots at ", current_price);
                    Print("🎯 Profit secured: ", NormalizeDouble(profit_distance / _Point, 0), " points");
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Advanced Breakeven Management                                    |
//+------------------------------------------------------------------+
void ManageAdvancedBreakeven() {
    double entry_price = position.PriceOpen();
    double current_price = (position.PositionType() == POSITION_TYPE_BUY) ?
                          SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                          SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    double breakeven_trigger = BreakevenPoints * _Point;

    // Enhanced breakeven with profit buffer
    if(position.PositionType() == POSITION_TYPE_BUY) {
        if(current_price >= entry_price + breakeven_trigger) {
            double new_sl = entry_price + (breakeven_trigger * 0.3); // Small profit buffer
            if(position.StopLoss() < new_sl) {
                if(trade.PositionModify(position.Ticket(), new_sl, position.TakeProfit())) {
                    Print("🛡️ ADVANCED BREAKEVEN SET! New SL: ", new_sl, " (with profit buffer)");
                }
            }
        }
    } else {
        if(current_price <= entry_price - breakeven_trigger) {
            double new_sl = entry_price - (breakeven_trigger * 0.3); // Small profit buffer
            if(position.StopLoss() > new_sl || position.StopLoss() == 0) {
                if(trade.PositionModify(position.Ticket(), new_sl, position.TakeProfit())) {
                    Print("🛡️ ADVANCED BREAKEVEN SET! New SL: ", new_sl, " (with profit buffer)");
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Advanced Trailing Stop Management                               |
//+------------------------------------------------------------------+
void ManageAdvancedTrailing() {
    double entry_price = position.PriceOpen();
    double current_price = (position.PositionType() == POSITION_TYPE_BUY) ?
                          SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                          SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    double trailing_start = TrailingStart * _Point;
    double trailing_step = TrailingStep * _Point;

    // Dynamic trailing based on volatility
    if(UseSmartRisk && daily_high > daily_low) {
        double daily_range = daily_high - daily_low;
        double volatility_multiplier = MathMax(0.5, MathMin(2.0, daily_range / (100 * _Point)));
        trailing_step *= volatility_multiplier;
    }

    if(position.PositionType() == POSITION_TYPE_BUY) {
        if(current_price >= entry_price + trailing_start) {
            double new_sl = current_price - trailing_step;
            if(new_sl > position.StopLoss()) {
                if(trade.PositionModify(position.Ticket(), new_sl, position.TakeProfit())) {
                    Print("📈 ADVANCED TRAILING UPDATED! New SL: ", new_sl);
                }
            }
        }
    } else {
        if(current_price <= entry_price - trailing_start) {
            double new_sl = current_price + trailing_step;
            if(new_sl < position.StopLoss() || position.StopLoss() == 0) {
                if(trade.PositionModify(position.Ticket(), new_sl, position.TakeProfit())) {
                    Print("📉 ADVANCED TRAILING UPDATED! New SL: ", new_sl);
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Smart Exit Based on Market Conditions                           |
//+------------------------------------------------------------------+
void ManageSmartExit() {
    double current_price = (position.PositionType() == POSITION_TYPE_BUY) ?
                          SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                          SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    // Check for reversal signals
    int current_pressure = DetectPressure();

    if(position.PositionType() == POSITION_TYPE_BUY && current_pressure == -1) {
        // Strong bearish pressure detected on long position
        if(position.Profit() > 0) { // Only if in profit
            Print("⚠️ SMART EXIT: Bearish pressure detected on LONG position - Closing for safety!");
            trade.PositionClose(position.Ticket());
        }
    } else if(position.PositionType() == POSITION_TYPE_SELL && current_pressure == 1) {
        // Strong bullish pressure detected on short position
        if(position.Profit() > 0) { // Only if in profit
            Print("⚠️ SMART EXIT: Bullish pressure detected on SHORT position - Closing for safety!");
            trade.PositionClose(position.Ticket());
        }
    }

    // Exit if price reaches extreme levels
    if(position.PositionType() == POSITION_TYPE_BUY) {
        if(current_price >= weekly_high * 0.999) { // Very close to weekly high
            Print("🎯 SMART EXIT: Price near weekly high - Taking profits!");
            trade.PositionClose(position.Ticket());
        }
    } else {
        if(current_price <= weekly_low * 1.001) { // Very close to weekly low
            Print("🎯 SMART EXIT: Price near weekly low - Taking profits!");
            trade.PositionClose(position.Ticket());
        }
    }
}

//+------------------------------------------------------------------+
//| Manage breakeven (Legacy function for compatibility)            |
//+------------------------------------------------------------------+
void ManageBreakeven() {
    double entry_price = position.PriceOpen();
    double current_price = (position.PositionType() == POSITION_TYPE_BUY) ?
                          SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                          SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    if(position.PositionType() == POSITION_TYPE_BUY) {
        if(current_price >= entry_price + BreakevenPoints * _Point) {
            if(position.StopLoss() < entry_price) {
                trade.PositionModify(position.Ticket(), entry_price, position.TakeProfit());
            }
        }
    } else {
        if(current_price <= entry_price - BreakevenPoints * _Point) {
            if(position.StopLoss() > entry_price) {
                trade.PositionModify(position.Ticket(), entry_price, position.TakeProfit());
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Manage trailing stop                                             |
//+------------------------------------------------------------------+
void ManageTrailingStop() {
    double entry_price = position.PriceOpen();
    double current_price = (position.PositionType() == POSITION_TYPE_BUY) ?
                          SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                          SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    if(position.PositionType() == POSITION_TYPE_BUY) {
        if(current_price >= entry_price + TrailingStart * _Point) {
            double new_sl = current_price - TrailingStep * _Point;
            if(new_sl > position.StopLoss()) {
                trade.PositionModify(position.Ticket(), new_sl, position.TakeProfit());
            }
        }
    } else {
        if(current_price <= entry_price - TrailingStart * _Point) {
            double new_sl = current_price + TrailingStep * _Point;
            if(new_sl < position.StopLoss()) {
                trade.PositionModify(position.Ticket(), new_sl, position.TakeProfit());
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Draw Order Block zones                                           |
//+------------------------------------------------------------------+
void DrawOrderBlocks() {
    // Clear existing OB objects
    ObjectsDeleteAll(0, ea_name + "_OB");

    // Draw bullish OBs
    for(int i = 0; i < ArraySize(bullish_obs); i++) {
        if(!bullish_obs[i].is_valid) continue;

        string obj_name = ea_name + "_OB_Bull_" + IntegerToString(i);
        ObjectCreate(0, obj_name, OBJ_RECTANGLE, 0,
                    bullish_obs[i].time, bullish_obs[i].high,
                    TimeCurrent() + PeriodSeconds(PERIOD_H1), bullish_obs[i].low);
        ObjectSetInteger(0, obj_name, OBJPROP_COLOR, BullishOBColor);
        ObjectSetInteger(0, obj_name, OBJPROP_STYLE, STYLE_SOLID);
        ObjectSetInteger(0, obj_name, OBJPROP_WIDTH, 1);
        ObjectSetInteger(0, obj_name, OBJPROP_FILL, true);
        ObjectSetInteger(0, obj_name, OBJPROP_BACK, true);
    }

    // Draw bearish OBs
    for(int i = 0; i < ArraySize(bearish_obs); i++) {
        if(!bearish_obs[i].is_valid) continue;

        string obj_name = ea_name + "_OB_Bear_" + IntegerToString(i);
        ObjectCreate(0, obj_name, OBJ_RECTANGLE, 0,
                    bearish_obs[i].time, bearish_obs[i].high,
                    TimeCurrent() + PeriodSeconds(PERIOD_H1), bearish_obs[i].low);
        ObjectSetInteger(0, obj_name, OBJPROP_COLOR, BearishOBColor);
        ObjectSetInteger(0, obj_name, OBJPROP_STYLE, STYLE_SOLID);
        ObjectSetInteger(0, obj_name, OBJPROP_WIDTH, 1);
        ObjectSetInteger(0, obj_name, OBJPROP_FILL, true);
        ObjectSetInteger(0, obj_name, OBJPROP_BACK, true);
    }
}

//+------------------------------------------------------------------+
//| Create dashboard                                                 |
//+------------------------------------------------------------------+
void CreateDashboard() {
    int x_start = 20;
    int y_start = 30;
    int line_height = 20;

    // Background panel
    string panel_name = ea_name + "_Panel";
    ObjectCreate(0, panel_name, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, panel_name, OBJPROP_XDISTANCE, x_start - 5);
    ObjectSetInteger(0, panel_name, OBJPROP_YDISTANCE, y_start - 5);
    ObjectSetInteger(0, panel_name, OBJPROP_XSIZE, 250);
    ObjectSetInteger(0, panel_name, OBJPROP_YSIZE, 150);
    ObjectSetInteger(0, panel_name, OBJPROP_COLOR, clrDarkBlue);
    ObjectSetInteger(0, panel_name, OBJPROP_BGCOLOR, clrNavy);
    ObjectSetInteger(0, panel_name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, panel_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);

    // Title
    string title_name = ea_name + "_Title";
    ObjectCreate(0, title_name, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, title_name, OBJPROP_XDISTANCE, x_start);
    ObjectSetInteger(0, title_name, OBJPROP_YDISTANCE, y_start);
    ObjectSetString(0, title_name, OBJPROP_TEXT, "Pressure OB EA v1.0");
    ObjectSetInteger(0, title_name, OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, title_name, OBJPROP_FONTSIZE, 10);
    ObjectSetString(0, title_name, OBJPROP_FONT, "Arial Bold");
}

//+------------------------------------------------------------------+
//| Update dashboard                                                 |
//+------------------------------------------------------------------+
void UpdateDashboard() {
    int x_start = 20;
    int y_start = 50;
    int line_height = 15;
    int current_y = y_start;

    // Trades today
    string trades_label = ea_name + "_TradesLabel";
    if(ObjectFind(0, trades_label) < 0) {
        ObjectCreate(0, trades_label, OBJ_LABEL, 0, 0, 0);
        ObjectSetInteger(0, trades_label, OBJPROP_XDISTANCE, x_start);
        ObjectSetInteger(0, trades_label, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(0, trades_label, OBJPROP_FONTSIZE, 8);
    }
    ObjectSetInteger(0, trades_label, OBJPROP_YDISTANCE, current_y);
    ObjectSetString(0, trades_label, OBJPROP_TEXT, "Trades Today: " + IntegerToString(trades_today) + "/" + IntegerToString(MaxTradesPerDay));
    current_y += line_height;

    // Current pressure
    int pressure = DetectPressure();
    string pressure_text = "No Pressure";
    color pressure_color = clrGray;
    if(pressure == 1) {
        pressure_text = "Bullish Pressure";
        pressure_color = clrLime;
    } else if(pressure == -1) {
        pressure_text = "Bearish Pressure";
        pressure_color = clrRed;
    }

    string pressure_label = ea_name + "_PressureLabel";
    if(ObjectFind(0, pressure_label) < 0) {
        ObjectCreate(0, pressure_label, OBJ_LABEL, 0, 0, 0);
        ObjectSetInteger(0, pressure_label, OBJPROP_XDISTANCE, x_start);
        ObjectSetInteger(0, pressure_label, OBJPROP_FONTSIZE, 8);
    }
    ObjectSetInteger(0, pressure_label, OBJPROP_YDISTANCE, current_y);
    ObjectSetString(0, pressure_label, OBJPROP_TEXT, "Pressure: " + pressure_text);
    ObjectSetInteger(0, pressure_label, OBJPROP_COLOR, pressure_color);
    current_y += line_height;

    // Order Blocks count
    string ob_label = ea_name + "_OBLabel";
    if(ObjectFind(0, ob_label) < 0) {
        ObjectCreate(0, ob_label, OBJ_LABEL, 0, 0, 0);
        ObjectSetInteger(0, ob_label, OBJPROP_XDISTANCE, x_start);
        ObjectSetInteger(0, ob_label, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(0, ob_label, OBJPROP_FONTSIZE, 8);
    }
    ObjectSetInteger(0, ob_label, OBJPROP_YDISTANCE, current_y);
    ObjectSetString(0, ob_label, OBJPROP_TEXT, "OBs: Bull=" + IntegerToString(ArraySize(bullish_obs)) + " Bear=" + IntegerToString(ArraySize(bearish_obs)));
    current_y += line_height;

    // Current time
    string time_label = ea_name + "_TimeLabel";
    if(ObjectFind(0, time_label) < 0) {
        ObjectCreate(0, time_label, OBJ_LABEL, 0, 0, 0);
        ObjectSetInteger(0, time_label, OBJPROP_XDISTANCE, x_start);
        ObjectSetInteger(0, time_label, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(0, time_label, OBJPROP_FONTSIZE, 8);
    }
    ObjectSetInteger(0, time_label, OBJPROP_YDISTANCE, current_y);
    ObjectSetString(0, time_label, OBJPROP_TEXT, "Time: " + TimeToString(TimeCurrent(), TIME_MINUTES));
    current_y += line_height;

    // Trading status
    string status_text = IsTradeTime() ? "ACTIVE" : "INACTIVE";
    color status_color = IsTradeTime() ? clrLime : clrRed;

    string status_label = ea_name + "_StatusLabel";
    if(ObjectFind(0, status_label) < 0) {
        ObjectCreate(0, status_label, OBJ_LABEL, 0, 0, 0);
        ObjectSetInteger(0, status_label, OBJPROP_XDISTANCE, x_start);
        ObjectSetInteger(0, status_label, OBJPROP_FONTSIZE, 8);
    }
    ObjectSetInteger(0, status_label, OBJPROP_YDISTANCE, current_y);
    ObjectSetString(0, status_label, OBJPROP_TEXT, "Status: " + status_text);
    ObjectSetInteger(0, status_label, OBJPROP_COLOR, status_color);
}
    